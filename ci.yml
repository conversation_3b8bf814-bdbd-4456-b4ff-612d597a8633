Global:
    version : 2.0
    mode : AGENT
    group_email: <EMAIL>

Default:
    profile : [changes]

Profiles:
    - profile:
      name : changes
      environment : 
          image: DECK_STD_CENTOS7
          tools:
              - nodejs: 20.latest
      build:
          command: sh ./build.sh offline
      artifacts:
          release: true
    - profile:
      name : online
      environment : 
          image: DECK_STD_CENTOS7
          tools:
              - nodejs: 20.latest
      build:
          command: sh ./build.sh online
      artifacts:
          release: true
    - profile:
      name : dev
      environment : 
          image: DECK_STD_CENTOS7
          tools:
              - nodejs: 20.latest
      build:
          command: sh ./build-qa.sh
      artifacts:
          release: true
    - profile:
      name : cnapBuild
      environment : 
          image: DECK_STD_CENTOS7
          tools:
              - nodejs: 20.latest
      build:
          command: sh ./build-cnap.sh
      artifacts:
          release: true
    - profile:
      name : cnapDevBuild
      environment : 
          image: DECK_STD_CENTOS7
          tools:
              - nodejs: 20.latest
      build:
          command: npm install -g @baidu/megep-jscov;autocov --version;autocov instrument src/ --wbox && sh ./build-cnap-dev.sh
      artifacts:
          release: true
