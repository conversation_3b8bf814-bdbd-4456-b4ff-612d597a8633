/**
 * @file 医生管理
 */

const doctorRouter = {
    path: '/doctor',
    name: 'doctor',
    title: '医生管理',
    component: () => import('@pages/base/template-nav.vue'),
    children: [
        // {
        //     title: '总医生列表详情',
        //     path: 'doctorAllDetail',
        //     name: 'doctor_all_detail',
        //     meta: {
        //         parent: 'doctor_all',
        //     },
        //     component: () => import('@pages/doctor/doctorDetail/drAllNewDetail.vue'),
        // },
        // {
        //     title: '医生详情',
        //     path: 'listDetail',
        //     name: 'doctor_list_detail',
        //     meta: {
        //         parent: 'doctor_list',
        //     },
        //     component: () => import('@pages/doctor/drListDetail.vue'),
        // },
        // // {
        // //     title: '医生不合格原因分布',
        // //     path: 'serverStatus',
        // //     name: 'doctor_server_status',
        // //     component: () => import('@pages/doctor/serverStatus.vue'),
        // // },
        // // {
        // //     title: '服务质量列表',
        // //     path: 'serviceQuality',
        // //     name: 'doctor_service_quality',
        // //     component: () => import('@pages/doctor/serviceQuality.vue'),
        // // },
        // // {
        // //     title: '医生操作历史',
        // //     path: 'updateRecord',
        // //     name: 'doctor_update_record',
        // //     component: () => import('@pages/doctor/updateRecord.vue'),
        // // },
        // // {
        // //     title: '医生职业列表',
        // //     path: 'careerList',
        // //     name: 'doctor_career_list',
        // //     component: () => import('@pages/doctor/careerList.vue'),
        // // },
        // // {
        // //     title: '未使用拇指的医生列表',
        // //     path: 'notuseList',
        // //     name: 'doctor_not_use_list',
        // //     component: () => import('@pages/doctor/drnotuseList.vue'),
        // // },
        // {
        //     title: '未使用拇指的医生详情',
        //     path: 'notuseListDetail',
        //     name: 'doctor_not_use_list_detail',
        //     meta: {
        //         parent: 'doctor_not_use_list',
        //     },
        //     component: () => import('@pages/doctor/drnotuseListDetail.vue'),
        // },
        // {
        //     title: '封禁医生',
        //     path: 'doctorForbbin',
        //     name: 'doctor_forbbin',
        //     component: () => import('@pages/doctor/drForbbin.vue'),
        // },
        // {
        //     title: '注册审核',
        //     path: 'doctorRegister',
        //     name: 'doctor_register',
        //     component: () => import('@pages/doctor/drReviewRegister.vue'),
        // },
        // {
        //     title: '医生邀请',
        //     path: 'doctorInvited',
        //     name: 'doctor_invited',
        //     component: () => import('@pages/doctor/drInvited.vue'),
        // },
        // {
        //     title: '报名列表',
        //     path: 'doctorSignList',
        //     name: 'doctor_sign_list',
        //     component: () => import('@pages/doctor/drClueList.vue'),
        // },
        // {
        //     title: '医生主页信息审核',
        //     path: 'doctorMainReview',
        //     name: 'doctor_main_review',
        //     component: () => import('@/pages/doctor/rightsreviewlist.vue'),
        // },
        // {
        //     title: '医生主页信息审核详情',
        //     path: 'doctorMainReviewDetail',
        //     name: 'doctor_main_review_detail',
        //     meta: {
        //         parent: 'doctor_main_review',
        //     },
        //     component: () => import('@pages/doctor/drMainInforReviewDetail.vue'),
        // },
        // {
        //     title: '医生主页审核信息查看',
        //     path: 'doctorMainReviewOnly',
        //     name: 'doctor_main_review_only',
        //     meta: {
        //         parent: 'doctor_main_review',
        //     },
        //     component: () => import('@pages/doctor/drMainInforReviewOnly.vue'),
        // },
        // {
        //     title: '免认证名单',
        //     path: 'doctorNotRegister',
        //     name: 'doctor_not_register',
        //     component: () => import('@pages/doctor/drnotRegister.vue'),
        // },
        // {
        //     title: '信誉资料审核',
        //     path: 'doctorTruthCheck',
        //     name: 'doctor_truth_check',
        //     component: () => import('@pages/doctor/doctorTruthCheck/index.vue'),
        // },
        // {
        //     title: '信誉资料审核',
        //     path: 'doctorTruthDetail',
        //     name: 'doctor_truth_detail',
        //     component: () => import('@/pages/doctor/doctorTruthCheck/doctorTruthDetail.vue'),
        // },
        // {
        //     title: '信誉资料审核新',
        //     path: 'doctorTruthNewDetail',
        //     name: 'doctor_truth_new_detail',
        //     component: () => import('@/pages/doctor/doctorTruthCheck/doctorTruthNewDetail.vue'),
        // },
        // {
        //     title: '名医卡开通审核',
        //     path: 'doctorCardReview',
        //     name: 'doctor_card_review',
        //     component: () => import('@pages/doctor/drCardReview.vue'),
        // },
        // {
        //     title: '科室迁移审核',
        //     path: 'depReview',
        //     name: 'dep_review',
        //     component: () => import('@pages/doctor/depReviewList.vue'),
        // },
        // {
        //     title: '科室迁移审核详情',
        //     path: 'depReviewDetail',
        //     name: 'dep_review_detail',
        //     component: () => import('@pages/doctor/depReviewDetail.vue'),
        // },
        // {
        //     title: '查看虚拟号绑定记录',
        //     path: 'bindRecord',
        //     name: 'bind_record',
        //     component: () => import('@pages/doctor/BindRecord'),
        // },
        // {
        //     title: '医生定向接诊要求审核',
        //     path: 'directReview',
        //     name: 'direct_review',
        //     component: () => import('@/pages/doctor/doctordiseaseauditlist.vue'),
        // },
        // {
        //     title: '医生定向接诊要求审核详情',
        //     path: 'directReviewDetail',
        //     name: 'direct_review_detail',
        //     meta: {
        //         parent: 'direct_review',
        //     },
        //     component: () => import('@pages/doctor/directReviewDetail.vue'),
        // },
        // {
        //     path: 'supportStaffList',
        //     title: '辅助医护人员列表',
        //     name: 'support_staff_list',
        //     component: () => import('@pages/assistant/supportStaffList.vue'),
        // },
        // {
        //     path: 'addAssistant',
        //     title: '添加辅助医护人员',
        //     name: 'addAssistant',
        //     component: () => import('@pages/assistant/addAssistant.vue'),
        // },
        // {
        //     path: 'assinstantDetail',
        //     title: '医护人员基础信息',
        //     name: 'assinstantDetail',
        //     component: () => import('@pages/assistant/assinstantDetail.vue'),
        // },
        // {
        //     path: 'doctorTeam',
        //     title: '医生团队列表',
        //     name: 'doctor_team',
        //     component: () => import('@/pages/team/doctorTeam.vue'),
        // },
        // {
        //     path: 'doctorTeamUpdate',
        //     title: '编辑团队资料',
        //     name: 'doctor_team_update',
        //     component: () => import('@pages/assistant/doctorTeamUpdate.vue'),
        // },
        // {
        //     path: 'employDoctorTeam',
        //     title: '自雇医生团队列表',
        //     name: 'employ_doctor_team',
        //     component: () => import('@pages/doctor/employ-dr-team/employDoctorTeam.vue'),
        // },
        // {
        //     path: 'employDoctorTeamDetail',
        //     title: '自雇医生团队详情',
        //     name: 'employ_doctor_team_detail',
        //     meta: {
        //         parent: 'employ_doctor_team',
        //     },
        //     component: () => import('@pages/doctor/employ-dr-team/employDoctorTeamDetail.vue'),
        // },
        // {
        //     path: 'employPersonnel',
        //     title: '辅助自雇人员管理列表',
        //     name: 'employ_personnel',
        //     component: () => import('@pages/doctor/employ-personnel/employPersonnel.vue'),
        // },
        // {
        //     path: 'employPersonnelDetail',
        //     title: '辅助自雇人员详情',
        //     name: 'employ_personnel_detail',
        //     meta: {
        //         parent: 'employ_personnel',
        //     },
        //     component: () => import('@pages/doctor/employ-personnel/employPersonnelDetail.vue'),
        // },
        // {
        //     path: 'updateEmployPersonnelDetail',
        //     title: '辅助自雇人员更新',
        //     name: 'update_employ_personnel_detail',
        //     meta: {
        //         parent: 'employ_personnel',
        //     },
        //     component: () => import('@pages/doctor/employ-personnel/details/employPersonnelUpdate.vue'),
        // },
        // {
        //     path: 'addEmployPersonnelDetail',
        //     title: '辅助自雇人员添加',
        //     name: 'add_employ_personnel_detail',
        //     meta: {
        //         parent: 'employ_personnel',
        //     },
        //     component: () => import('@pages/doctor/employ-personnel/details/employPersonnelAdd.vue'),
        // },
        // {
        //     path: 'namelist',
        //     title: '名单列表',
        //     name: 'name_list',
        //     component: () => import('@pages/doctor/namelist.vue'),
        // },
        // {
        //     path: 'namedetails',
        //     title: '名单详情',
        //     name: 'name_details',
        //     component: () => import('@pages/doctor/nameDetails.vue'),
        // },
        // {
        //     path: 'doctorsearch',
        //     title: '医生消息记录',
        //     name: 'doctor_msg_record',
        //     component: () => import('@pages/doctor/doctormsgRecord.vue'),
        // },
        // {
        //     path: 'doctorinvitecode',
        //     title: '医生入驻邀请码管理',
        //     name: 'doctor_invite_code',
        //     component: () => import('@pages/doctor/doctorinviteCode.vue'),
        // },
        // {
        //     path: 'doctorinvitecodedetail',
        //     title: '医生明细',
        //     name: 'doctor_invite_code_detail',
        //     component: () => import('@pages/doctor/doctorinviteCodeDetail.vue'),
        // },
        // {
        //     path: 'medstaffAuditList',
        //     title: '辅助自营人员信息审核',
        //     name: 'medstaff_audit_list',
        //     component: () => import('@/pages/doctor/medstaffAuditList.vue'),
        // },
        {
            title: '医生列表',
            path: '/drList',
            name: 'doctor_list',
            component: () => import('@pages/doctor/drList.vue'),
        },
        {
            title: '医生详情',
            path: 'listDetail',
            name: 'doctor_list_detail',
            meta: {
                parent: 'doctor_list',
            },
            component: () => import('@pages/doctor/drListDetail.vue'),
        },
        {
            title: '医生不合格原因分布',
            path: 'serverStatus',
            name: 'doctor_server_status',
            component: () => import('@pages/doctor/serverStatus.vue'),
        },
        {
            title: '服务质量列表',
            path: 'serviceQuality',
            name: 'doctor_service_quality',
            component: () => import('@pages/doctor/serviceQuality.vue'),
        },
        {
            title: '医生操作历史',
            path: 'updateRecord',
            name: 'doctor_update_record',
            component: () => import('@pages/doctor/updateRecord.vue'),
        },
        {
            title: '医生职业列表',
            path: 'careerList',
            name: 'doctor_career_list',
            component: () => import('@pages/doctor/careerList.vue'),
        },
        {
            title: '未使用拇指的医生列表',
            path: 'notuseList',
            name: 'doctor_not_use_list',
            component: () => import('@pages/doctor/drnotuseList.vue'),
        },
        {
            title: '未使用拇指的医生详情',
            path: 'notuseListDetail',
            name: 'doctor_not_use_list_detail',
            meta: {
                parent: 'doctor_not_use_list',
            },
            component: () => import('@pages/doctor/drnotuseListDetail.vue'),
        },
        {
            title: '封禁医生',
            path: 'doctorForbbin',
            name: 'doctor_forbbin',
            component: () => import('@pages/doctor/drForbbin.vue'),
        },
        {
            title: '注册审核',
            path: 'doctorRegister',
            name: 'doctor_register',
            component: () => import('@pages/doctor/drReviewRegister.vue'),
        },
        {
            title: '医生邀请',
            path: 'doctorInvited',
            name: 'doctor_invited',
            component: () => import('@pages/doctor/drInvited.vue'),
        },
        {
            title: '报名列表',
            path: 'doctorSignList',
            name: 'doctor_sign_list',
            component: () => import('@pages/doctor/drClueList.vue'),
        },
        {
            title: '医生主页信息审核',
            path: 'doctorMainReview',
            name: 'doctor_main_review',
            component: () => import('@pages/doctor/drMainInforReview.vue'),
        },
        {
            title: '医生主页信息审核详情',
            path: 'doctorMainReviewDetail',
            name: 'doctor_main_review_detail',
            meta: {
                parent: 'doctor_main_review',
            },
            component: () => import('@pages/doctor/drMainInforReviewDetail.vue'),
        },

        {
            title: '医美医生信息审核',
            path: 'ymdoctorMainReview',
            name: 'ymdoctor_main_review',
            component: () => import('@pages/doctor/ymdrMainInforReview.vue'),
        },
        {
            title: '医生主页信息审核详情',
            path: 'ymdoctorMainReviewDetail',
            name: 'ymdoctor_main_review_detail',
            meta: {
                parent: 'ymdoctor_main_review',
            },
            component: () => import('@pages/doctor/ymdrMainInforReviewDetail.vue'),
        },
        {
            title: '医美医生信息审核查看',
            path: 'ymdoctorMainReviewOnly',
            name: 'ymdoctor_main_review_only',
            meta: {
                parent: 'ymdoctor_main_review',
            },
            component: () => import('@pages/doctor/ymdrMainInforReviewOnly.vue'),
        },

        {
            title: '医生主页审核信息查看',
            path: 'doctorMainReviewOnly',
            name: 'doctor_main_review_only',
            meta: {
                parent: 'doctor_main_review',
            },
            component: () => import('@pages/doctor/drMainInforReviewOnly.vue'),
        },
        {
            title: '免认证名单',
            path: 'doctorNotRegister',
            name: 'doctor_not_register',
            component: () => import('@pages/doctor/drnotRegister.vue'),
        },
        {
            title: '信誉资料审核',
            path: 'doctorTruthCheck',
            name: 'doctor_truth_check',
            component: () => import('@pages/doctor/doctorTruthCheck/index.vue'),
        },
        {
            title: '信誉资料审核',
            path: 'doctorTruthDetail',
            name: 'doctor_truth_detail',
            component: () => import('@/pages/doctor/doctorTruthCheck/doctorTruthDetail.vue'),
        },
        {
            title: '信誉资料审核新',
            path: 'doctorTruthNewDetail',
            name: 'doctor_truth_new_detail',
            component: () => import('@/pages/doctor/doctorTruthCheck/doctorTruthNewDetail.vue'),
        },
        {
            title: '名医卡开通审核',
            path: 'doctorCardReview',
            name: 'doctor_card_review',
            component: () => import('@pages/doctor/drCardReview.vue'),
        },
        {
            title: '科室迁移审核',
            path: 'depReview',
            name: 'dep_review',
            component: () => import('@pages/doctor/depReviewList.vue'),
        },
        {
            title: '科室迁移审核详情',
            path: 'depReviewDetail',
            name: 'dep_review_detail',
            component: () => import('@pages/doctor/depReviewDetail.vue'),
        },
        {
            title: '查看虚拟号绑定记录',
            path: 'bindRecord',
            name: 'bind_record',
            component: () => import('@pages/doctor/BindRecord'),
        },
        {
            title: '医生定向接诊要求审核',
            path: 'directReview',
            name: 'direct_review',
            component: () => import('@pages/doctor/directReview.vue'),
        },
        {
            title: '医生定向接诊要求审核详情',
            path: 'directReviewDetail',
            name: 'direct_review_detail',
            meta: {
                parent: 'direct_review',
            },
            component: () => import('@pages/doctor/directReviewDetail.vue'),
        },
        {
            path: 'supportStaffList',
            title: '辅助医护人员列表',
            name: 'support_staff_list',
            component: () => import('@pages/assistant/supportStaffList.vue'),
        },
        {
            path: 'addAssistant',
            title: '添加辅助医护人员',
            name: 'addAssistant',
            component: () => import('@pages/assistant/addAssistant.vue'),
        },
        {
            path: 'assinstantDetail',
            title: '医护人员基础信息',
            name: 'assinstantDetail',
            component: () => import('@pages/assistant/assinstantDetail.vue'),
        },
        {
            path: 'doctorTeam',
            title: '医生团队列表',
            name: 'doctor_team',
            component: () => import('@pages/assistant/doctorTeam.vue'),
        },
        {
            path: 'doctorTeamUpdate',
            title: '编辑团队资料',
            name: 'doctor_team_update',
            component: () => import('@pages/assistant/doctorTeamUpdate.vue'),
        },
        {
            path: 'employDoctorTeam',
            title: '自雇医生团队列表',
            name: 'employ_doctor_team',
            component: () => import('@pages/doctor/employ-dr-team/employDoctorTeam.vue'),
        },
        {
            path: 'employDoctorTeamDetail',
            title: '自雇医生团队详情',
            name: 'employ_doctor_team_detail',
            meta: {
                parent: 'employ_doctor_team',
            },
            component: () => import('@pages/doctor/employ-dr-team/employDoctorTeamDetail.vue'),
        },
        {
            path: 'employPersonnel',
            title: '辅助自雇人员管理列表',
            name: 'employ_personnel',
            component: () => import('@pages/doctor/employ-personnel/employPersonnel.vue'),
        },
        {
            path: 'employPersonnelDetail',
            title: '辅助自雇人员详情',
            name: 'employ_personnel_detail',
            meta: {
                parent: 'employ_personnel',
            },
            component: () => import('@pages/doctor/employ-personnel/employPersonnelDetail.vue'),
        },
        {
            path: 'updateEmployPersonnelDetail',
            title: '辅助自雇人员更新',
            name: 'update_employ_personnel_detail',
            meta: {
                parent: 'employ_personnel',
            },
            component: () => import('@pages/doctor/employ-personnel/details/employPersonnelUpdate.vue'),
        },
        {
            path: 'addEmployPersonnelDetail',
            title: '辅助自雇人员添加',
            name: 'add_employ_personnel_detail',
            meta: {
                parent: 'employ_personnel',
            },
            component: () => import('@pages/doctor/employ-personnel/details/employPersonnelAdd.vue'),
        },
        {
            path: 'namelist',
            title: '名单列表',
            name: 'name_list',
            component: () => import('@pages/doctor/nameList.vue'),
        },
        {
            path: 'namedetails',
            title: '名单详情',
            name: 'name_details',
            component: () => import('@pages/doctor/nameDetails.vue'),
        },
        {
            path: 'doctorinvitecode',
            title: '医生入驻邀请码管理',
            name: 'doctor_invite_code',
            component: () => import('@pages/doctor/doctorinviteCode.vue'),
        },
        {
            path: 'doctorinvitecodedetail',
            title: '医生明细',
            name: 'doctor_invite_code_detail',
            component: () => import('@pages/doctor/doctorinviteCodeDetail.vue'),
        },
        {
            path: 'medstaffAuditList',
            title: '辅助自营人员信息审核',
            name: 'medstaff_audit_list',
            component: () => import('@/pages/doctor/medstaffAudit/medstaffAuditList.vue'),
        },
        {
            path: 'medstaffAuditDetail',
            title: '辅助自营人员信息审核详情',
            name: 'medstaff_audit_detail',
            meta: {
                parent: 'medstaff_audit_list',
            },
            component: () => import('@/pages/doctor/medstaffAudit/medstaffAuditDetail.vue'),
        },
        {
            path: 'doctorPunishRecord',
            title: '医生处理记录',
            name: 'doctor_punish_record',
            component: () => import('@/pages/doctor/punishRecord/index.vue'),
        },
        {
            path: 'testModal',
            title: '测试模态框',
            name: 'test_modal',
            component: () => import('@/pages/doctor/doctorCategory/test-modal.vue'),
        },
    ],
};

export default doctorRouter;
