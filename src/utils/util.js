import dayjs from 'dayjs';

const util = {};
util.title = function (title) {
    title = title || '百度健康';
    window.document.title = title;
};

util.inOf = function (arr, targetArr) {
    let res = true;
    if (arr && arr.length > 0) {
        arr.map(item => {
            if (targetArr.indexOf(item) < 0) {
                res = false;
            }
        });
    }
    return res;
};

util.oneOf = function (ele, targetArr) {
    if (targetArr.indexOf(ele) >= 0) {
        return true;
    }
    return false;

};

util.showThisRoute = function (itAccess, currentAccess) {
    if (typeof itAccess === 'object' && itAccess.isArray()) {
        return util.oneOf(currentAccess, itAccess);
    }
    return itAccess === currentAccess;

};

util.getRouterObjByName = function (routers, name) {
    let routerObj = {};
    routers.forEach(item => {
        if (item.name === 'otherRouter') {
            item.children.forEach((child, i) => {
                if (child.name === name) {
                    routerObj = item.children[i];
                }
            });
        } else {
            if (item.children.length === 1) {
                if (item.children[0].name === name) {
                    routerObj = item.children[0];
                }
            } else {
                item.children.forEach((child, i) => {
                    if (child.name === name) {
                        routerObj = item.children[i];
                    }
                });
            }
        }
    });
    return routerObj;
};

util.handleTitle = function (vm, item) {
    return item.title;
};

util.setCurrentPath = function (vm, name) {
    let title = '';
    let isOtherRouter = false;
    vm.$store.state.routers.forEach(item => {
        if (item.children && item.children.length === 1) {
            if (item.children[0].name === name) {
                title = util.handleTitle(vm, item);
                if (item.name === 'otherRouter') {
                    isOtherRouter = true;
                }
            }
        } else {
            item.children && item.children.forEach(child => {
                if (child.name === name) {
                    title = util.handleTitle(vm, child);
                    if (item.name === 'otherRouter') {
                        isOtherRouter = true;
                    }
                }
            });
        }
    });
    let currentPathArr = [];
    if (name === 'home_index') {
        currentPathArr = [{
            title: util.handleTitle(vm, util.getRouterObjByName(vm.$store.state.routers, 'home_index')),
            path: '',
            name: 'home_index',
        }];
    } else if ((name.indexOf('_index') >= 0 || isOtherRouter) && name !== 'home_index') {
        currentPathArr = [{
            title: util.handleTitle(vm, util.getRouterObjByName(vm.$store.state.routers, 'home_index')),
            path: '/home',
            name: 'home_index',
        },
        {
            title: title,
            path: '',
            name: name,
        },
        ];
    } else {
        const currentPathObj = vm.$store.state.routers.filter(item => {
            if (item.children.length <= 1) {
                return item.children[0].name === name;
            }
            let i = 0;
            const childArr = item.children;
            const len = childArr.length;
            while (i < len) {
                if (childArr[i].name === name) {
                    return true;
                }
                i++;
            }
            return false;

        })[0];
        if (currentPathObj.children.length <= 1 && currentPathObj.name === 'home') {
            currentPathArr = [{
                title: '首页',
                path: '',
                name: 'home_index',
            }];
        } else if (currentPathObj.children.length <= 1 && currentPathObj.name !== 'home') {
            currentPathArr = [{
                title: '首页',
                path: '/home',
                name: 'home_index',
            },
            {
                title: currentPathObj.title,
                path: '',
                name: name,
            },
            ];
        } else {
            const childObj = currentPathObj.children.filter(child => {
                return child.name === name;
            })[0];
            currentPathArr = [{
                title: '首页',
                path: '/home',
                name: 'home_index',
            },
            {
                title: currentPathObj.title,
                path: '',
                name: currentPathObj.name,
            },
            {
                title: childObj.title,
                path: currentPathObj.path + '/' + childObj.path,
                name: name,
            },
            ];
        }
    }
    vm.$store.commit('setCurrentPath', currentPathArr);

    return currentPathArr;
};

util.toDefaultPage = function (routers, name, route, next) {
    const len = routers.length;
    let i = 0;
    let notHandle = true;
    while (i < len) {
        if (routers[i].name === name && routers[i].redirect === undefined) {
            if (!routers[i].children) {
                break;
            }
            let name = 'error_401';
            const defaultRoute = routers[i].children.find(element => {
                return element.meta && element.meta.auth;
            });
            if (defaultRoute) {
                name = defaultRoute.name;
            }
            route.replace({
                name: name,
                // name: routers[i].children[0].name
            }, function onComplete() {
                next();
            }, function onAbort(err) {
                if (err) {
                    console.log('路由错误或您可能已跳转至当前路由');
                }
            });
            notHandle = false;
            break;
        }
        i++;
    }
    if (notHandle) {
        next();
    }
};

util.getFirstMatchedRoute = function (matched) {
    return matched && matched[0].name;
};

util.getRouteMap = function (routes, routeMap = {}) {
    routes.forEach(route => {
        routeMap[route.name] = route;
        if (route.children && route.children.length) {
            util.getRouteMap(route.children, routeMap);
        }
    });
    return routeMap;
};

util.setRouteMeta = function (route) {
    if (!route.meta) {
        route.meta = {
            auth: true,
        };
    } else {
        route.meta.auth = true;
    }
};

util.getRouteForMenuList = function (menuList, routeMap) {
    for (const menu of menuList) {
        const name = menu.url;
        const text = menu.name;
        // const type = menu.viewType;

        if (routeMap[name]) {
            menu.name = name;
            menu.text = text;
            menu.url = routeMap[name].path;
            // 反向设置route的meta信息，标识当前用户有权限访问该路由
            util.setRouteMeta(routeMap[name]);
        }
        /*  else if (+type === 2) {
            menu.name = name;
        } */
        else {
            console.error(menu.name + ' 菜单项目没有相应的route，请检查是否配置正确: ', name);
        }

        if (menu.son && menu.son.length) {
            util.getRouteForMenuList(menu.son, routeMap);
        }
    }
    // console.log(menuList, 'menuList')
    return menuList;
};
util.clone = function (deep, obj) {
    const target = deep || {};
    let cloneType = '';
    for (const o in obj) {
        const t = this.type(obj[o]);
        if (t !== '[object Array]' && t !== '[object Object]') {
            target[o] = obj[o];
        } else if (obj[o] !== undefined) {
            if (t === '[object Array]') {
                cloneType = [];
            } else if (t === '[object Object]') {
                cloneType = {};
            }
            target[o] = this.clone(cloneType, obj[o]);
        }
    }
    return target;
};
util.type = function type(obj) {
    return Object.prototype.toString.call(obj);
};

util.getOffsetTopFunc = function (ele, offsetY) {

    if (!offsetY) {
        offsetY = 0;
    }

    offsetY += ele.offsetTop;
    const parent = ele.offsetParent;
    if (parent === document.body) {
        return offsetY;
    }
    return util.getOffsetTopFunc(parent, offsetY);

};

util.getQueryValue = function (key) {
    const url = window.location.href;
    const obj = {};
    const search = url.split('?');
    if (search && search.length > 1) {
        const strs = search[1].split('&');
        for (let i = 0; i < strs.length; i++) {
            obj[strs[i].split('=')[0]] = decodeURIComponent(strs[i].split('=')[1]);
        }
        if (key) {
            return obj[key];
        }
        return obj;
    }
};

util.getHashQueryValue = function (key) {
    const url = window.location.search || window.location.hash;
    const obj = {};
    const search = url.split('?');
    if (search && search.length > 1) {
        const strs = search[1].split('&');
        for (let i = 0; i < strs.length; i++) {
            obj[strs[i].split('=')[0]] = decodeURIComponent(strs[i].split('=')[1]);
        }
        if (key) {
            return obj[key];
        }
        return obj;
    }
};

util.getCookie = function (name) {
    // var path = 'path=/';
    // var domain = 'domain=.baidu.com';
    // document.cookie = path + '; ' + domain;

    const cookies = document.cookie;
    const list = cookies.split('; ');
    for (let i = 0; i < list.length; i++) {
        const arr = list[i].split('=');
        if (arr[0] === name) {
            return decodeURIComponent(arr[1]);
        }
    }
};
util.setCookie = function (cname, cvalue) {
    let d = new Date();
    d = new Date(d.setDate(d.getDate() + 365));
    d = new Date(d.toLocaleDateString());
    const expires = 'expires=' + d.toUTCString();
    // console.log('UVexpires=' + d);

    const path = 'path=/';
    const domain = 'domain=zhaopin.baidu.com';
    document.cookie = cname + '=' + cvalue + '; ' + expires + '; ' + path + '; ' + domain;
};

// 简单的节流函数
util.throttle = (func, wait, mustRun) => {
    let timeout;
    let startTime = new Date();

    return function (...args) {
        const context = this;
        const curTime = new Date();

        clearTimeout(timeout);
        // 如果达到了规定的触发时间间隔，触发 handler
        if (curTime - startTime >= mustRun) {
            func.apply(context, args);
            startTime = curTime;
            // 没达到触发间隔，重新设定定时器
        } else {
            timeout = setTimeout(func, wait);
        }
    };
};

util.getScrollTop = function () {
    const scrollTop = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop || 0;
    return scrollTop;
};

util.getClientHeight = function () {
    let clientHeight = 0;
    if (document.body.clientHeight && document.documentElement.clientHeight) {
        clientHeight = document.body.clientHeight < document.documentElement.clientHeight
            ? document.body.clientHeight : document.documentElement.clientHeight;
    } else {
        clientHeight = document.body.clientHeight > document.documentElement.clientHeight
            ? document.body.clientHeight : document.documentElement.clientHeight;
    }
    return clientHeight;
};

util.getScrollHeight = function () {
    return Math.max(document.body.scrollHeight, document.documentElement.scrollHeight);
};

util.seconds2date = function (v) {
    if (!v) {
        return 0;
    }
    v = v + '000';
    const d = new Date(Number(v));
    const m = d.getMonth() + 1;
    const f = function (n) {
        return n < 10 ? '0' + n : n;
    };
    return d.getFullYear() + '.' + f(m) + '.' + f(d.getDate());
};

util.date2seconds = s => {
    if (!s) {
        return s;
    }
    const d = new Date(s);
    return d.getTime() / 1000;
};

util.seconds2ymd = function (v) {
    if (v === 0) {
        return '';
    }
    v = v + '000';
    const d = new Date(Number(v));
    const m = d.getMonth() + 1;
    const f = function (n) {
        return n < 10 ? '0' + n : n;
    };
    return d.getFullYear() + '.' + f(m) + '.' + f(d.getDate());
};

util.seconds2time = function (v) {
    if (v === 0) {
        return 0;
    }
    v = v + '000';
    const d = new Date(Number(v));
    const m = d.getMonth() + 1;
    const f = function (n) {
        return n < 10 ? '0' + n : n;
    };
    return d.getFullYear() + '.' + f(m) + '.' + f(d.getDate())
        + ' ' + f(d.getHours()) + ':' + f(d.getMinutes()) + ':' + f(d.getSeconds());
};

util.filterPicId = function (url = '') {
    const regResult = url.match(/^https:\/\/[a-zA-Z-\/_\.]+\/(\d+)/);
    return regResult ? regResult[1] : console.log('图片id正则校验失败');
};

util.openNewUrl = function (url = '', params = {}, name = '_blank') {
    let p = '';
    for (const key in params) {
        if (params.hasOwnProperty(key)) {
            const element = params[key];
            p += key + '=' + encodeURIComponent(element) + '&';
        }
    }
    p = p.slice(0, -1);
    url = url + '?' + p;
    window.open(url, name);
};

util.arrToStr = function (val) {
    const arr = [];
    val.forEach(item => {
        const str = item ? item.join(',') : '';
        arr.push(str);
    });
    return arr;
};

util.getType = function (obj) {
    return Object.prototype.toString.call(obj);
};

util.deepClone = function (deep, obj) {
    const target = deep || {};
    let cloneType = '';
    for (const o in obj) {
        const t = this.getType(obj[o]);
        if (t !== '[object Array]' && t !== '[object Object]') {
            target[o] = obj[o];
        } else if (obj[o] !== undefined) {
            if (t === '[object Array]') {
                cloneType = [];
                for (let i = 0; i < obj[o].length; i++) {
                    obj[o][i].checked = false;
                }
            } else if (t === '[object Object]') {
                cloneType = {};
                for (const i in obj) {
                    obj[i].checked = false;
                }
            }

            target[o] = util.deepClone(cloneType, obj[o]);
        }
    }
    return target;
};

// 身份证校验
util.idCheck = function (params) {
    if (params) {
        return !!/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(params);
    }
    return false;
};

// 手机号码校验
util.mobileCheck = function (params) {
    if (params) {
        return !!/^1\d{10}$/.test(params);
    }
    return false;
};

// 获取今天从0点开始的时间戳 unix
util.unixInitTime = function (date) {
    if (!date) {
        return '';
    }
    return Math.round(new Date(new Date(date).getTime()).setHours(0, 0, 0, 0) / 1000);
};

// 获取从当前 23:59:59的时间戳 unix
util.unixLastTime = function (date) {
    if (!date) {
        return '';
    }
    return Math.round(new Date(new Date(date).getTime()).setHours(23, 59, 59) / 1000);
};
// '0' '1' 互相转换 false true
util.transformStrAndBoolean = function (target) {
    const type = typeof target;
    if (type === 'string') {
        return !!(+target);
    }
    if (type === 'boolean') {
        return +target + '';
    }
    return target;
};
// 创建image实例 然后返回base64
util.createBase64Image = function (src, cb) {
    const image = new Image();
    image.src = src + '?v=' + Math.random(); // 处理缓存
    image.crossOrigin = '*'; // 支持跨域图片
    image.onload = function () {
        const base64 = getBase64Image(image);
        cb && cb(base64);
    };
};

// 生成base74
function getBase64Image(img) {
    const canvas = document.createElement('canvas');
    canvas.width = img.width;
    canvas.height = img.height;
    const ctx = canvas.getContext('2d');
    ctx.drawImage(img, 0, 0, img.width, img.height);
    const dataURL = canvas.toDataURL('image/png'); // 可选其他值 image/jpeg
    return dataURL;
};

// 秒转为时间格式
util.formatSecToDate = function (sec = 0) {
    const min = Math.floor(sec % 3600); // 分钟
    const f = function (n) {
        return n < 10 ? '0' + n : n;
    };
    return f(Math.floor(sec / 3600)) + ':' + f(Math.floor(min / 60)) + ':' + f(sec % 60);
};
/**
 * @description 钱单位转化
 * @param {*} value 待转化值
 * @param {*} unit 转化的单元
 * @param {*} length 小数点后保留有效数字数
 * @returns 转化后的价格
 */
export function transMoney(value, unit = 100, length = 1) {
    if (!value) {
        return '0.0';
    }
    return (Number(value) / unit).toFixed(length);
}

// 两个对象深度对比
util.compare = function (origin, target) {
    // 判断参数一是否为object类型
    if (typeof target === 'object') {
        // 判断参数二是否不为object类型
        if (typeof origin !== 'object') return false;
        // 判断参数一和参数二的属性数量是否一致
        if (Object.keys(origin).length !== Object.keys(target).length) return false;
        // 遍历参数二的所有键
        for (const key in target)
            // 判断参数二的键值和参数一的键值是否一致
            if (!this.compare(origin[key], target[key])) return false;
        // 参数1和参数2对比一致
        return true;
    }
    return origin === target;// 属性值的对比结果
};

// 历史页面尽量不改变页面路径
const routerUrlMap = {
    'nrMcnBind': '/doctor/addmcndoctor',
    'nrContentExam': '/doctor/examList',
    'supportStaffList': '/assistant/supportStaffList',
    'authValidate': '/tools/authValidate',
    'doctorTeam': '/team/doctorTeam',
    'employPersonnel': '/doctor/employ-personnel/employPersonnel',
    'employDoctorTeam': '/doctor/employ-dr-team/employDoctorTeam',
    'twoLevelChannel': '/channel/twoLevelChannel/twoLevelChannelWrap',
    'channelMapping': '/channel/channelMapping/channelMapping',
    'doctorInviteCode': '/doctor/doctorinviteCode',
    'nameList': '/doctor/nameList',
    'nrMcnList': '/nr/mcn/mcnList',
    'skuConfigure': '/tools/SkuConfigure/SkuConfigureList',
    'skuRecommender': '/tools/skuRecommend/skuRecommender',
    'distributionLogic': '/consult/distribution_logic',
    'claimLimitManage': '/consult/treatPatient/treatPatient',
    'couponList': '/tools/coupon/couponList',
    'couponTool': '/tools/coupon/couponTools',
    'couponPlan': '/tools/coupon/couponPlan',
    'salePlan': '/tools/sale/salePlan',
    'newConsultListAll': '/consult/newConsultList',
    'refundSubsidy': '/consult/refundSubsidy/refundSubsidy',
    'aiSetting': '/tools/aiSetting/aiSetting',
    'workConfig': '/tools/workConfig',
    'linkList': '/tools/recommend-link/linkList',
    'refundTool': '/tools/refund/refundTool',
    'consultCommentList': '/consult/commentList',
    'consultComplaints': '/consult/complaints',
    'consultReportList': '/consult/reportList',
    'commonWords': '/doctor/commonWords',
    'getwelcomevoicelist': '/doctor/welcomeVoice',
    'absentMechanism': '/add/absentMechanism/absentMechanismList',
    'hospitalQualificationList': '/gh/hospitalQualification',
    'hospitalReviewList': '/gh/hospitalReviewList',
    'registeredList': '/gh/registeredList',
    'ordersList': '/gh/order',
    'independentSubmissionList': '/gh/independentSubmissionList',
    'diagnoseList': '/gh/diagnose',
    'sdiagnoseList': '/gh/stopDiagnose',
    'operationWorkOrder': '/gh/operationWorkOrder/operationWorkOrderList',
    'appealList': '/gh/appeal',
    'messageSet': '/gh/messageSet',
    'promotionSettings': '/gh/promotion_settings',
    'noVisit': '/gh/no_visit',
    'ecInformationAudit': '/gh/ECInformationAudit',
    'accessFee': '/gh/accessFee/accessFeeList',
    'doctorQualificationList': '/gh/doctorQualification',
    'fictiTpSetList': '/ghsvc/fictiTpSet/fictiTpSetList',
    'orderList': '/ghsvc/order/orderList',
    'doctorReportQrCode': '/doctorpatient/reportQRcode',
    'doctorReportPatientReview': '/doctorpatient/reportPatientReview',
    'diseaseManage': '/doctorpatient/diseaseManage/diseaseConfig',
    'tagManage': '/doctorpatient/tagManage',
    'patientTagManage': '/doctorpatient/patientTagManage',
    'questionnaireDemandList': '/doctorpatient/follow/questionnaireDemandList',
    'questionnaireList': '/doctorpatient/follow/questionnaireList',
    'followUpSettings': '/doctorpatient/follow_up_settings',
    'visitDemandManage': '/doctorpatient/visit/visitDemandManage/visitDemandList',
    'qcListAll': '/qc/qcListAll',
    'qcTaskListAll': '/qc/qcTaskListAll',
    'qcAppealListNew': '/qc/qcAppealListNew',
    'qcTaskConfiguration': '/qc/qcTaskConfiguration',
    'qcPriorityCofing': '/qc/priorityCofing',
    'qcTpqesList': '/qc/tpQesList',
    'yilaiDoctorRegList': '/hospital/doctorRegList',
    'prescriptionAudit': '/hospital/prescriptionAudit',
    'tpManager': '/hospital/tp_manager',
    'drugCatalogue': '/hospital/drugCatalogue',
    'drugCataexamine': '/hospital/drugCataexamine',
    'drugDemand': '/hospital/drugDemand',
    'prescriptionList': '/hospital/prescription/prescription',
    'drugSearch': '/tools/drug/drugSearch',
    'yilaiDigitalPrescription': '/hospital/digitalPrescription',
    'yilaiTranslist': '/hospital/translist',
    'yilaiElectronicMedicalRecord': '/hospital/electronicMedicalRecord',
    'drugControlConfig': '/tools/drugControlConfig',
    'developTool': '/tools/developTool/developTool',
    'drugReview': '/hospital/drugReview/index',
    'pbPharmacistSchedule': '/hospital/pharmacistScheduleList',
    'pbDoctorList': '/pb/directionalScheduling',
    'pbStatistical': '/pb/directionalSchedulingStatistacal',
    'weightedConfig': '/pb/weightedConfig',
    'achieveAllCollect': '/achieve/achieveCollect',
    'achieveAxSummary': '/achieve/achieveAxSummary',
    'achieveVideoSummary': '/achieve/achieveVideoSummary',
    'achieveTelSummary': '/achieve/achieveTelSummary',
    'schedulingPerformance': '/achieve/schedulingPerformance',
    'achieveQcSummary': '/achieve/achieveQcSummary',
    'achieveDrSummary': '/achieve/achieveDrSummary',
    'achievePharmacistSummary': '/achieve/achievePharmacistSummary',
    'achieveHzMind': '/achieve/patientsMind',
    'achieveHzbdReward': '/achieve/achieveHzbdReward',
    'platformReward': '/achieve/achievePlatform',
    'achieveContentReward': '/achieve/achieveContentReward',
    'directionalCollection': '/achieve/directionalCollection',
    'achieveServicePackage': '/achieve/achieveServicePackage',
    'achieveBankCard': '/achieve/achieveBankCard',
    'achieveSupplyPush': '/achieve/achieveSupplyPush',
    'achieveDividePush': '/achieve/achieveDividePush',
    'achieveAvoicePush': '/achieve/achieveAvoicePush',
    'achieveHarvestPushThirdparty': '/achieve/achieveHarvestPush',
    'achieveDividePushThirdparty': '/achieve/achieveDividePush',
    'achieveAvoicePushThirdparty': '/achieve/achieveAvoicePush',
    'monthFinanceCollect': '/achieve/monthFinanceCollect',
    'articalList': '/tools/articalList',
    'smsList': '/tools/smsList',
    'insideMsgList': '/tools/insideMsgList',
    'activityPopConfigpage': '/tools/invit/activityPopConfigPage/activityPop',
    'appScreen': '/tools/appScreen',
    'docInvitDoc': '/tools/invit/docInvitDoc',
    'turntableConfig': '/tools/turntable/turntableList',
    'turntablePrizeInfo': '/tools/turntable/turntablePrize',
    'docCheckInActivity': '/tools/invit/docInvitCheckIn',
    'taskList': '/task/taskList/index',
    'distributionRecord': '/task/distributionRecord',
    'newComerActivity': '/tools/invit/newComerActivity',
    'pointsExchange': '/task/point/pointsExchange',
    'pointsStatistics': '/task/pointsStatistics/pointsList',
    'npsSurveyConfig': '/tools/npsConfig/npsConfig',
    'doctorSatisfactionList': '/tools/interrogation/doctorSatisfactionList',
    'appUpgrade': '/tools/appUpgrade/appUpgrade',
    'wzcBannerConf': '/wzc/doctorBrandCard/brandBannerConfig',
    'wzcResourceConf': '/wzc/resource/resourceList',
    'systemPermissionCreate': '/system/permissionCreate',
    'systemRoleList': '/system/roleList',
    'systemUserList': '/system/userList',
    'systemActionLog': '/system/actionLog',
    'phonePass': '/tools/phonePass',
    'uidPass': '/tools/uidPass',
    'muzhiIdUid': '/tools/muzhiidUid',
    'toolsetQidEncry': '/tools/qidEncry',
    'toolsetBjhInfor': '/tools/bjhInfor',
    'toolsetAgentInfo': '/tools/agentInfo',
    'doctorIdCard': '/tools/doctorIdCard',
    'doctorMsgRecord': '/doctor/doctormsgRecord',
    'toolsetRedisTool': '/tools/redisTool',
    'queryTool': '/tools/queryTool',
    'doctorPatientList': '/doctorpatient/doctorPatientList',
    'phoneServiceList': '/consult/phoneServiceList',
    'videoServiceList': '/consult/videoServiceList',
    'doctorPunishRecord': '/doctor/punishRecord/index',
    'ymdoctorMainReview': '/doctor/ymdrMainInforReview',
    'messageRecord': '/tools/messageRecord/index',
    'approveInquire': '/tools/approveInquire/approveList',
    'hospitalManage': '/gh/hospitalManage',
    'doctorAuditList': '/gh/doctorAuditList',
    'doctorManageList': '/gh/doctorManageList',
    'enterpriseAuditList': '/gh/enterpriseAuditList',
    'enterpriseManage': '/gh/enterpriseManage',
    'broadcastList': '/gh/broadcastList',
    'broadcastDetail': '/gh/broadcastDetail',
    'ddDiagnoseList': '/gh/ddDiagnoseList',
    'ddStopDiagnoseList': '/gh/ddStopDiagnoseList',
    'ddRegisteredList': '/gh/ddRegisteredList',
};

export const getMapMenu = data => {
    const res = [];
    data.forEach(item => {
        const newItem = {};

        if (!item.son) {
            newItem.component = resolve =>
                import(`@/pages${routerUrlMap[item.url] || item.permission_identify}.vue`).then(module => {
                    return module.default;
                });
            newItem.title = item.name;
            newItem.path = (item.son && item.url && `/${item.url}`) || item.url;
            newItem.name = item.url;
            newItem.url = item.url;
        }
        else {
            if (item.url) {
                newItem.redirect = to => {
                    return `/${item.url}/${item.son[0].son[0].url}`;
                };
            }
            newItem.title = item.name;
            newItem.name = item.url;
            newItem.url = item.url;
            newItem.permission_identify = item.permission_identify;
            newItem.component = () => (item.url
                ? import('@pages/base/template-nav.vue') : import('@pages/base/view.vue'));
            newItem.path = (item.url && item.url.indexOf('/') === -1) ? `/${item.url}` : item.url;
            newItem.children = getMapMenu(item.son);
        }
        res.push(newItem);

    });
    return res;
};

// https://github.com/iamkun/dayjs
export function dateFormat(time, dataFormat) {
    if (!time) {
        return '';
    }

    if (typeof time === 'number' && time.toString().length === 10) {
        time = time * 1000;
    }

    const date = new Date(time);
    return dayjs(date).format(dataFormat || 'YYYY-MM-DD');
}

// 过滤空参数
export function filterEmpty(obj) {
    const newObj = {};
    Object.keys(obj).forEach(key => {
        if (obj[key] !== '' && obj[key] !== null && obj[key] !== undefined) {
            newObj[key] = obj[key];
        }
    });
    return newObj;
}

/**
 * 格式化HTML字符串，去除所有class并将文本节点中的空格替换为 &nbsp;
 *
 * @param {string} htmlString - 待格式化的HTML字符串
 * @returns {string} 格式化后的HTML字符串
 * @throws 如果处理HTML字符串时发生错误，将抛出错误并在控制台打印错误信息，同时返回原始HTML字符串
 */
export function formatHtml(htmlString) {
    try {
        const template = document.createElement('template');

        /* bca-disable */
        template.innerHTML = htmlString;

        const elements = template.content.querySelectorAll('*');

        elements.forEach(element => {
            element.removeAttribute('class');

            // 替换文本节点中的空格为 &nbsp;
            element.childNodes.forEach(node => {
                if (node.nodeType === Node.TEXT_NODE) {
                    node.textContent = node.textContent?.replace(/ /g, '\u00A0');
                }
            });
        });

        return template.innerHTML;
    } catch (error) {
        console.error('Failed to process HTML string:', error);
        return htmlString;
    }
}
export default util;
