<template>
    <div class="create-msg">
        <i-title
            title="站内信配置"
            link-url="insideMsgList"
            sub-title="新建站内信"
        />
        <i-form
            ref="formMsg"
            :model="formData"
            :rules="ruleMsg"
            :label-width="120"
        >
            <i-form-item prop="is_all_dr" label="目标用户：">
                <i-radio-group v-model="formData.is_all_dr" @on-change="clearItem">
                    <i-radio label="0">自定义</i-radio>
                    <i-radio label="1">名单圈选</i-radio>
                </i-radio-group>
                <i-input
                    v-show="formData.is_all_dr === '0'"
                    v-model="formData.target_dr"
                    type="textarea"
                    placeholder="请输入医生UID,例如：263237;21312"
                />
            </i-form-item>
            <i-form-item
                v-show="+formData.is_all_dr === 1"
                prop="doc_group_id"
                label="名单圈选："
            >
                <mz-name-list-search
                    v-model="formData.doc_group_id"
                    placeholder="搜索名单id/名单名称"
                />
            </i-form-item>
            <i-form-item prop="target_time" label="发送时间：">
                <i-date-picker
                    type="datetime"
                    format="yyyy-MM-dd HH:mm"
                    @on-change="handleChangeDate"
                />
            </i-form-item>
            <i-form-item prop="msg_title" label="消息标题：">
                <i-input
                    v-model="formData.msg_title" :maxlength="50"
                    show-word-limit
                />
            </i-form-item>
            <i-form-item prop="msg_body" label="消息内容：">
                <mz-quill-editor
                    :config="quillEditorConfig"
                    class="editor"
                    @change="quillContentchange"
                />
            </i-form-item>
            <i-form-item prop="need_push" label="是否推送push：">
                <i-radio-group v-model="formData.need_push">
                    <i-radio :label="1">是</i-radio>
                    <i-radio :label="0">否</i-radio>
                </i-radio-group>
            </i-form-item>
            <i-form-item
                v-if="formData.need_push === 1"
                prop="tpl_id" label="消息分类："
            >
                <i-radio-group v-model="formData.tpl_id">
                    <i-radio
                        v-for="(item, key) in tplMap"
                        :key="key"
                        :label="key"
                    >
                        {{ item }}
                        <span style="color: red">{{ tplToolMap[key] }}</span>
                    </i-radio>
                </i-radio-group>
            </i-form-item>
            <i-form-item>
                <i-button
                    type="success" shape="circle"
                    @click="submit"
                >提交</i-button>
            </i-form-item>
        </i-form>
    </div>
</template>

<script>
/**
 * 新建站内信
 */
import Api from '@/api/tools';
import IButton from '@components/button';
import IDatePicker from '@components/date-picker';
import IForm from '@components/form';
import IFormItem from '@components/form-item';
import IInput from '@components/input';
import IRadio from '@components/radio';
import IRadioGroup from '@components/radio-group';
import ITitle from '@components/mz-title';
import Util from '@/utils/util';
import MzQuillEditor from '@BusComponents/mz-quill-editor';
import MzNameListSearch from '@BusComponents/mz-name-list-search/index.vue';
import { quillEditorConfig } from './toolsMap';
import dayjs from 'dayjs';


export default {
    components: {
        IButton,
        IDatePicker,
        IInput,
        IForm,
        IFormItem,
        IRadio,
        IRadioGroup,
        ITitle,
        MzQuillEditor,
        MzNameListSearch,
    },
    data() {
        this.tplToolMap = {
            20069: '各手机厂商有每日推送数量限制，请合理使用push资源',
            20070: '无数量限制。但如果分类不符，可能会被手机厂商处罚',
        };
        const validateTargetDr = (rule, value, callback) => {
            if (!+value && !this.formData.target_dr) {
                return callback(new Error('请输入自定义医生UID'));
            }
            callback();

        };
        const validateDrId = (rule, value, callback) => {
            if (+this.formData.is_all_dr === 1 && !value) {
                return callback(new Error('请选择名单圈选'));
            }
            callback();

        };
        return {
            formData: {
                is_all_dr: '',
                target_dr: '',
                target_time: '',
                msg_title: '',
                msg_body: '',
                need_push: '',
                doc_group_id: null,
            },
            ruleMsg: {
                is_all_dr: [{ validator: validateTargetDr, tigger: 'change' }],
                doc_group_id: [{ required: true, validator: validateDrId, tigger: 'change' }],
                target_time: [{ required: true, type: 'number', message: '请选择发送时间', trigger: 'change' }],
                msg_title: [{ required: true, message: '请输入消息标题', trigger: 'blur' }],
                msg_body: [{ required: true, message: '请输入消息内容', trigger: 'blur' }],
                need_push: [{
                    required: true,
                    type: 'number',
                    message: '请选择是否推送push',
                    trigger: 'change',
                }],
                tpl_id: [{
                    required: true,
                    type: 'number',
                    validator: (rule, value, callback) => {
                        if (this.formData.need_push === 1 && this.formData.tpl_id) {
                            callback();
                        } else {
                            callback(new Error('请选择消息分类'));
                        }
                    },
                    trigger: 'change',
                }],
            },
            customTarget: '', // 自定义医生
            quillEditorConfig,
            tplMap: {},
        };
    },
    created() {
        this.getConfig();
    },
    methods: {
        clearItem() {
            this.formData.target_dr = '';
            this.formData.doc_group_id = null;
        },
        async getConfig() {
            const { data, errno } = await this.$http.post(Api.getMsgConfig);

            if (errno === 0) {
                this.tplMap = data.station_msg_tpl;
            }
        },
        submit() {
            this.$refs.formMsg.validate(async valid => {
                if (valid) {
                    if (this.formData.need_push === 0) {
                        delete this.formData.tpl_id;
                    }
                    const { need_push, is_all_dr, ...params } = this.formData;
                    if (this.formData.need_push !== 0) {
                        params.need_push = need_push;
                    }
                    params.is_doc_group = Number(!!this.formData.doc_group_id);
                    const { errno } = await this.$http.post(
                        Api.submitMsg, params,
                        {
                            headers: {
                                'Content-type': 'application/x-www-form-urlencoded',
                            },
                        });
                    if (errno === 0) {
                        this.$Message.success({
                            content: '已发起审批，请在如流查看',
                            duration: 3.5,
                        });
                        this.$router.push('/bOperator/insideMsgList');
                    }
                }
            });
        },
        handleChangeDate(date) {
            this.formData.target_time = dayjs(date).unix();
        },
        quillContentchange(text) {
            this.formData = {
                ...this.formData,
                msg_body: text.html,
            };
        },
    },
};
</script>

<style lang="less" scoped>
.create-msg {
    width: 760px;
}
.editor {
    height: 200px;
}
.ivu-form-item {
    &:nth-child(6) {
        margin-top: 56px;
    }
}
/deep/ .ivu-time-picker-cells-cell-selected, .ivu-time-picker-cells-cell-selected:hover{
    color: #fff;
}
</style>
