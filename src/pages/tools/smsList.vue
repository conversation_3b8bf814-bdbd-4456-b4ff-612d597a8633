<template>
    <div class="dr-help-register">
        <i-title title="短信发送记录" />
        <base-search
            class="searchWrap"
            :search-list="searchList"
            :search-fields="searchFields"
            @onSubmit="changePage(1)"
        >
            <i-button
                slot="extraAction"
                class="c-gap-right-small"
                type="success"
                shape="circle"
                @click="createSms"
            > 新建 </i-button>
        </base-search>

        <div class="c-gap-top">
            <i-table
                :loading="loading" class="c-gap-bottom"
                border :columns="columns"
                :data="list"
            />
            <i-page
                v-show="total > 20"
                :total="total"
                :current="pn"
                :page-size="rn"
                flex="main:center"
                show-total
                @on-change="changePage"
            />
        </div>
        <doctor-category-modal
            :id="doctorCategoryId"
            v-model="showDoctorCategoryModal"
            title="查看名单"
            disabled
            :data="doctorCategoryData"
        />
    </div>
</template>

<script>
/**
 * 发送短信 列表
 */
import Api from '@/api/tools';
import IButton from '@components/button';
import IForm from '@components/form';
import IInput from '@components/input';
import IPage from '@components/page';
import ITable from '@components/table';
import ITitle from '@components/mz-title';
import DatePicker from '@components/date-picker';
import dayjs from 'dayjs';
import Util from '@/utils/util';
import { isDateRangeWithin90Days } from './smsUtils';
import doctorCategoryModal from '@/pages/doctor/doctorCategory/doctorCategoryModal.vue';
import useDoctorCategory from '@/pages/doctor/doctorCategory/useDoctorCategory';
export default {
    components: {
        IButton,
        IPage,
        ITable,
        ITitle,
        doctorCategoryModal,
    },
    data() {
        this.SENDING = 5; // 发送中
        this.columns = [
            {
                title: 'ID',
                key: 'id',
                align: 'center',
                minWidth: 70,
            },
            {
                title: '发送时间',
                key: 'targetTime',
                align: 'center',
                minWidth: 150,
            },
            {
                title: '消息分类',
                key: 'templateId',
                align: 'center',
                minWidth: 120,
                render: (h, { row }) => {
                    if (!row.templateId) (<span>-</span>);
                    return (
                        <span>
                            {this.smsMap?.[row.templateId]}
                        </span>
                    );
                },
            },
            {
                title: '目标用户',
                key: 'docGroupId',
                align: 'center',
                minWidth: 150,
                render: (h, { row }) => {
                    if (!row.docGroupId) return (<span>-</span>);
                    return (
                        <a onClick={() => this.handleShowDoctorCategoryModal(row.docGroupId)}>
                            {row.docGroupId + ' ' + row.docGroupName}
                        </a>
                    );
                },
            },
            {
                title: '发送人数',
                key: 'targetNum',
                align: 'center',
                minWidth: 70,
            },
            {
                title: '消息内容',
                key: 'msgBody',
                align: 'center',
                minWidth: 400,
            },
            {
                title: '发送状态',
                key: 'status_text',
                align: 'center',
                minWidth: 100,
                render: (h, { row: { sendNum = '', targetNum = '', statusText, status } }) => {
                    if (status === this.SENDING) {
                        return (
                            <div>
                                <p>{statusText}</p>
                                <p>（{sendNum} / {targetNum}）</p>
                            </div>
                        );
                    }
                    return (<span>{statusText}</span>);
                },
            },
            {
                title: '操作人',
                key: 'operator',
                align: 'center',
                minWidth: 100,
            },
        ];
        return {
            searchList: {
                timeRange: '',
                status: '',
            },
            searchFields: [
                {
                    type: 'extra',
                    render: h => (
                        <DatePicker
                            v-model={this.searchList.timeRange}
                            type="datetimerange"
                            placeholder="发送时间"
                            style="width: 320px;"
                        />
                    ),
                },
                {
                    key: 'status',
                    type: 'extra',
                    render: h => (
                        <i-select
                            v-model={this.searchList.status}
                            placeholder='任务状态'
                            clearable
                        >
                            {
                              Object.entries(this.statusMap)?.map(([key, label]) => (
                                  <i-option
                                    value={key}
                                    label={label}
                                />
                              ))
                            }
                        </i-select>
                    ),
                },
            ],
            loading: false,
            total: 0,
            pn: 1,
            rn: 20,
            params: {

            },

            list: [],
            statusMap: {},
            smsMap: {},
        };
    },
    async created() {
        await this.getConfig();
        this.getList();
    },
    methods: {
        changePage(page) {
            this.pn = page;
            this.getList();
        },
        async getConfig() {
            const { data, errno } = await this.$http.post(Api.getMsgConfig);

            if (errno === 0) {
                this.statusMap = data.sms_status || {};
                this.smsMap = data.sms_tpl || {};
            }
        },
        async getList() {
            const params = {
                ...this.searchList,
                pn: this.pn,
                rn: this.rn,
            };

            if (this.searchList.timeRange[0]) {
                const start = dayjs(this.searchList.timeRange[0]).unix();
                const end = dayjs(this.searchList.timeRange[1]).unix();
                if (!isDateRangeWithin90Days(start, end)) {
                    this.$Message.error('消息日期时间段不能超过90天');
                    return;
                }
                params.timeRange = `${start}-${end}`;
            } else {
                params.timeRange = '';
            }
            this.loading = true;
            const { data, errno } = await this.$http.get(Api.smslist, {
                params,
            });

            if (errno === 0) {
                this.list = data.list;
                this.total = data.total;
            }

            this.loading = false;
        },
        createSms() {
            this.$router.push('/bOperator/smsSend');
        },
    },
    setup() {
        const {
            doctorCategoryId,
            doctorCategoryData,
            showDoctorCategoryModal,
            handleShowDoctorCategoryModal,
        } = useDoctorCategory();

        return {
            doctorCategoryId,
            doctorCategoryData,
            showDoctorCategoryModal,
            handleShowDoctorCategoryModal,
        };
    },
};
</script>

<style lang="less" scoped>
/deep/ .ivu-time-picker-cells-cell-selected, .ivu-time-picker-cells-cell-selected:hover{
    color: #fff;
}
</style>
