<template>
    <div class="dr-help-register">
        <i-title title="站内信配置" />
        <base-search
            class="searchWrap"
            :search-list="searchList"
            :search-fields="searchFields"
            @onSubmit="changePage(1)"
        >
            <i-button
                slot="extraAction"
                class="c-gap-right-small"
                type="success"
                shape="circle"
                @click="createMsg"
            > 新建 </i-button>
        </base-search>

        <div class="c-gap-top">
            <i-table
                :loading="loading" class="c-gap-bottom"
                border :columns="columns"
                :data="list"
            />
            <i-page
                v-show="total > 20"
                :total="total"
                :current="pn"
                :page-size="rn"
                flex="main:center"
                show-total
                @on-change="changePage"
            />
        </div>
        <doctor-category-modal
            :id="doctorCategoryId"
            v-model="showDoctorCategoryModal"
            title="查看名单"
            disabled
            :data="doctorCategoryData"
        />
    </div>
</template>

<script>
/**
 * 站内信列表
 */
import Api from '@/api/tools';
import IButton from '@components/button';
import IForm from '@components/form';
import IInput from '@components/input';
import IPage from '@components/page';
import ITable from '@components/table';
import ITitle from '@components/mz-title';
import dayjs from 'dayjs';
import Util from '@/utils/util';
import DatePicker from '@components/date-picker';
import { isDateRangeWithin90Days } from './smsUtils';
import doctorCategoryModal from '@/pages/doctor/doctorCategory/doctorCategoryModal.vue';
import useDoctorCategory from '@/pages/doctor/doctorCategory/useDoctorCategory';

export default {
    components: {
        IButton,
        IPage,
        ITable,
        ITitle,
        doctorCategoryModal,
    },
    data() {
        this.columns = [
            {
                title: 'ID',
                key: 'id',
                align: 'center',
                minWidth: 70,
            },
            {
                title: '发送时间',
                key: 'target_time',
                align: 'center',
                minWidth: 170,
                render: (h, params) => {
                    return h('span', Util.seconds2time(params.row.target_time));
                },
            },
            {
                title: '目标用户',
                key: 'doc_group_id',
                align: 'center',
                minWidth: 150,
                render: (h, { row }) => {
                    if (!row.doc_group_id) return (<span>-</span>);
                    return (
                        <a onClick={() => this.handleShowDoctorCategoryModal(row.doc_group_id)}>
                            {row.doc_group_id + ' ' + row.doc_group_name}
                        </a>
                    );
                },
            },
            {
                title: '发送人数',
                key: 'target_num',
                align: 'center',
                minWidth: 70,
            },
            {
                title: '消息标题',
                key: 'msg_title',
                align: 'center',
                minWidth: 200,
            },
            {
                title: '消息内容',
                key: 'msg_body',
                align: 'center',
                minWidth: 300,
            },
            {
                title: 'push',
                key: 'need_push',
                align: 'center',
                minWidth: 80,
                render: (h, { row: { need_push } }) => {
                    return h('span', need_push ? '是' : '否');
                },
            },
            {
                title: 'push消息分类',
                key: 'tpl_id',
                align: 'center',
                minWidth: 150,
                render: (h, { row }) => {
                    return (<span>{this.tplMap[row.tpl_id] || '-'}</span>);
                },
            },
            {
                title: '发送状态',
                key: 'status_text',
                align: 'center',
                minWidth: 100,
            },
            {
                title: '操作人',
                key: 'operator',
                align: 'center',
                minWidth: 100,
            },
        ];
        return {
            searchList: {
                tpl_id: null,
                msg_title: '',
                status: null,
                time_range: '',
            },
            searchFields: [
                {
                    type: 'extra',
                    render: h => (
                        <DatePicker
                            type="datetimerange"
                            placeholder="发送时间"
                            style="width: 320px"
                            v-model={this.searchList.time_range}
                        />
                    ),
                },
                {
                    key: 'msg_title',
                    type: 'input',
                    placeholder: '消息标题',
                },
                {
                    key: 'status',
                    type: 'extra',
                    render: h => (
                        <i-select
                            v-model={this.searchList.status}
                            placeholder='任务状态'
                            clearable
                        >
                            {
                              Object.entries(this.statusMap)?.map(([key, label]) => (
                                  <i-option
                                    value={key}
                                    label={label}
                                />
                              ))
                            }
                        </i-select>
                    ),
                },
                {
                    key: 'tpl_id',
                    type: 'extra',
                    render: h => (
                        <i-select
                            v-model={this.searchList.tpl_id}
                            placeholder='push消息分类'
                            clearable
                        >
                            {
                              Object.entries(this.tplMap)?.map(([key, label]) => (
                                  <i-option
                                    value={key}
                                    label={label}
                                />
                              ))
                            }
                        </i-select>
                    ),

                },
            ],
            loading: false,
            total: 0,
            pn: 1,
            rn: 20,
            params: {
                id: '',
            },

            list: [],
            tplMap: {},
            statusMap: {},
            };
    },
    async created() {
        await this.getConfig();
        this.getList();
    },
    methods: {
        createMsg() {
            this.$router.push('/bOperator/createMsg');
        },
        changePage(page) {
            this.pn = page;
            this.getList();
        },
        async getList() {
            const params = {
                ...this.searchList,
                pn: this.pn,
                rn: this.rn,
            };

            if (this.searchList.time_range[0]) {
                const start = dayjs(this.searchList.time_range[0]).unix();
                const end = dayjs(this.searchList.time_range[1]).unix();
                if (!isDateRangeWithin90Days(start, end)) {
                    this.$Message.error('消息日期时间段不能超过90天');
                    return;
                }
                params.time_range = `${start}-${end}`;
            } else {
                params.time_range = '';
            }

            this.loading = true;

            const { data, errno } = await this.$http.get(Api.insideMsgList, {
                params,
            });

            if (errno === 0) {
                this.list = data.list;
                this.total = data.total;
            }

            this.loading = false;
        },
        async getConfig() {
            const { data, errno } = await this.$http.post(Api.getMsgConfig);

            if (errno === 0) {
                this.statusMap = data.station_msg_status || {};
                this.tplMap = data.station_msg_tpl || {};
            }
        },
    },
    setup() {
        const {
            doctorCategoryId,
            doctorCategoryData,
            showDoctorCategoryModal,
            handleShowDoctorCategoryModal,
        } = useDoctorCategory();

        return {
            doctorCategoryId,
            doctorCategoryData,
            showDoctorCategoryModal,
            handleShowDoctorCategoryModal,
        };
    },
};
</script>

<style lang="less" scoped>
/deep/ .ivu-time-picker-cells-cell-selected, .ivu-time-picker-cells-cell-selected:hover{
    color: #fff;
}
</style>
