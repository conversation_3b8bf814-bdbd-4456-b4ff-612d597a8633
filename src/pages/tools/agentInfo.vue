<template>
    <div class="agent-detail-container">
        <i-title
            title="智能体平台信息"
        />

        <div class="search-section">
            uid：
            <uid-search
                class="uid-search"
                :default-uid="$route.query.uid"
                @onSearch="handleUidSearch"
                @onClear="handleUidClear"
            />
            <i-button
                type="primary"
                @click="handleSearch"
            >
                查询
            </i-button>
        </div>

        <div class="list c-gap-top">
            <div class="bjh_json">
                <h3>智能体id和信息接口</h3>
                <div v-if="baseContent" style="margin: 10px;">
                    <JsonView
                        :value="baseContent"
                        :expand-depth="5"
                        boxed
                    />
                </div>
                <div
                    v-else class="no-data"
                    style="margin-top: 30px;"
                >暂无数据</div>
            </div>
            <div class="trust_json">
                <h3>智能体标签接口</h3>
                <div v-if="tagContent" style=" margin: 10px;">
                    <JsonView
                        :value="tagContent"
                        :expand-depth="5"
                        boxed
                    />
                </div>
                <div
                    v-else class="no-data"
                    style="margin-top: 30px;"
                >暂无数据</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import Api from '@/api/doctor';
import Button from '@components/button';
import Form from '@components/form';
import Input from '@components/input';
import Table from '@components/table';
import UidSearch from '@BusComponents/mz-uid-search/index.vue';
import Title from '@components/mz-title';
import JsonView from 'vue-json-viewer';
import { ref, onMounted, getCurrentInstance } from 'vue';
import { useRoute } from '@/hooks/router';


import iInput from '@components/input';
import iButton from '@components/button';
import iCard from '@components/card';
import iSpin from '@components/spin';

const route = useRoute();
const baseContent = ref('');
const tagContent = ref('');
const loading = ref(false);
const isEmptyList = ref(false);
const searchUid = ref(route.query.uid);
const { proxy: ctx } = getCurrentInstance();

const handleSearch = async () => {
    if (!searchUid.value) {
        ctx.$Message.error('请输入uid');
        return;
    }
    const res = await ctx.$http.get(Api.getAgentInfo, {
        params: {
            uid: searchUid.value,
        },
    });

    if (res?.errno === 0) {
        baseContent.value = res.data.base_info;
        tagContent.value = res.data.tag_info;
    }
};

const handleUidSearch = value => {
    searchUid.value = value;
};

const handleUidClear = () => {
    searchUid.value = '';
};

onMounted(() => {
    if (route.query.uid) {
        handleSearch();
    }
});


</script>

<style lang="less" scoped>

.agent-detail-container {
    padding: 20px;

    .search-section {
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .uid-search{
        margin-right: 12px;
    }

    /deep/ .ivu-form-item {
        margin-bottom: 0;
    }

    .list {
        display: flex;
        flex-direction: row;
        justify-content: left;
        .bjh_json,
        .trust_json{
            overflow: auto;
            flex: 1;
            min-width: 200px;
            margin-bottom: 20px;
        }
    }
}
</style>
