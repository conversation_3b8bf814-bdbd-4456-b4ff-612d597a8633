<template>
    <div class="artical-create">
        <i-title
            title="公告列表" link-url="articalList"
            :sub-title="title"
        />
        <i-steps class="step" :current="current">
            <i-step title="配置物料" />
            <i-step title="选择目标人群" />
            <i-step title="完成" />
        </i-steps>
        <i-form
            v-if="current === 0"
            ref="formItem" :model="formItem"
            :label-width="120" :rules="ruleValidate"
            class="inner-box c-gap-left-large"
        >
            <form-item label="投放位置：" prop="type">
                <i-select v-model="formItem.type" placeholder="请选择公告投放位置">
                    <i-option
                        v-for="val in typeOption"
                        :key="val.code"
                        :label="val.name"
                        :value="val.code"
                    />
                </i-select>
            </form-item>
            <form-item label="优先级设置：" prop="priority">
                <i-input
                    v-model="formItem.priority" type="text"
                    placeholder=""
                />
                <span>当前优先级最大值：100</span>
            </form-item>
            <form-item
                v-if="showContentDispatch"
                label="内容分发："
                prop="garden_standard_category"
            >
                <i-select
                    v-model="formItem.garden_standard_category"
                    placeholder="请选择内容分发"
                >
                    <i-option
                        v-for="item in contentDispatch[formItem.type]"
                        :key="item.key"
                        :label="item.value"
                        :value="item.key"
                    />
                </i-select>
            </form-item>
            <form-item label="标签" prop="tag">
                <i-input
                    v-model="formItem.tag" type="text"
                    maxlength="4" show-word-limit
                />
            </form-item>
            <form-item label="公告标题：" prop="title">
                <div flex="dir:top">
                    <i-input
                        v-model="formItem.title" type="text"
                        placeholder="" class="left"
                    />
                    <mz-img-upload
                        v-show="formItem.type !== 10"
                        class="c-gap-top-small"
                        :action="imgApi"
                        :data="{pic_type: 1, from: ''}"
                        :default-list="formItem.title_pics"
                        @beforeUpload="handleBeforeUpload"
                        @del="removeImg"
                        @uploadSuccess="uploadOk"
                    />
                </div>
            </form-item>
            <form-item label="公告内容：" prop="is_third">
                <radio-group v-model="formItem.is_third">
                    <i-radio :label="0">
                        <span>编辑页面</span>
                    </i-radio>
                    <i-radio :label="1">
                        <span>外链</span>
                    </i-radio>
                </radio-group>
                <mz-quill-editor
                    v-show="formItem.is_third === 0"
                    ref="MzQuillEditor"
                    :show-text-count="true"
                    :img-upload-url="imgApi"
                    :video-upload-url="videoApi"
                    :value="defaultQuillValue"
                    @change="quillContentchange"
                />
                <p v-show="formItem.is_third === 1">
                    外链URL：<i-input v-model="formItem.third_url" type="text" />
                </p>
            </form-item>
            <form-item
                v-if="+formItem.is_third === 0" label="微信分享摘要："
                prop="description" style="margin-top:60px"
            >
                <i-input
                    v-model="formItem.description" type="textarea"
                    placeholder="请输入公告描述" :maxlength="50"
                    show-word-limit
                />
            </form-item>
            <form-item
                label="是否展示吸底按钮"
                style="margin-top:60px"
            >
                <radio-group v-model="formItem.is_show_bottom_btn">
                    <i-radio :label="1">
                        <span>是</span>
                    </i-radio>
                    <i-radio :label="0">
                        <span>否</span>
                    </i-radio>
                </radio-group>
            </form-item>
            <form-item
                v-if="showBtnConfig"
                label="按钮文案"
                prop="bottom_btn_text"
            >
                <i-input
                    v-model="formItem.bottom_btn_text"
                    placeholder="请填写按钮文案"
                    type="text"
                    maxlength="8" show-word-limit
                />
            </form-item>
            <form-item
                v-if="showBtnConfig"
                label="按钮链接"
                prop="bottom_btn_link"
            >
                <i-input
                    v-model="formItem.bottom_btn_link"
                    type="textarea"
                    placeholder="请填写按钮链接"
                    maxlength="300"
                    show-word-limit
                    :autosize="{minRows: 3}"
                />
            </form-item>
            <form-item
                label="是否商业化文章"
                prop="is_commercialization"
            >
                <radio-group v-model="formItem.is_commercialization">
                    <i-radio :label="1">
                        <span>是</span>
                    </i-radio>
                    <i-radio :label="0">
                        <span>否</span>
                    </i-radio>
                </radio-group>
            </form-item>
            <form-item
                label="是否允许用户分享"
                prop="is_share"
            >
                <radio-group v-model="formItem.is_share">
                    <i-radio :label="1">
                        <span>是</span>
                    </i-radio>
                    <i-radio :label="0">
                        <span>否</span>
                    </i-radio>
                </radio-group>
            </form-item>
            <form-item
                label="是否进行用户行为数据埋点"
                prop="is_ubc"
            >
                <radio-group v-model="formItem.is_ubc">
                    <i-radio :label="1">
                        <span>是</span>
                    </i-radio>
                    <i-radio :label="0">
                        <span>否</span>
                    </i-radio>
                </radio-group>
            </form-item>
            <form-item
                label="是否支持在端外可查看"
                prop="out_view"
            >
                <radio-group v-model="formItem.out_view">
                    <i-radio :label="1">
                        <span>是</span>
                    </i-radio>
                    <i-radio :label="0">
                        <span>否</span>
                    </i-radio>
                </radio-group>
            </form-item>
            <form-item label="" style="margin-top:60px">
                <i-button
                    class="c-gap-right-small"
                    type="success"
                    shape="circle"
                    @click="submitUpdate('formItem')"
                >
                    提交
                </i-button>
            </form-item>
        </i-form>
        <div v-if="current === 1" class="target-user">
            <doctor-category
                :id="nameId"
                :data="doctorCategoryData"
                @submit="submitDoctorCategory"
            />
        </div>
    </div>
</template>

<script>
import ApiTools from '@/api/tools';
import ApiNr from '@/api/nr';
import ApiDoctor from '@/api/doctor';
import ITitle from '@components/mz-title';
import IInput from '@components/input';
import IForm from '@components/form';
import FormItem from '@components/form-item';
import ISelect from '@components/select';
import IOption from '@components/option';
import IRadio from '@components/radio';
import RadioGroup from '@components/radio-group';
import IButton from '@components/button';
import ISteps from '@components/steps';
import IStep from '@components/step';
import FilterData, { platform_topic_code, platform_list_code, doctor_list_code, serve_list_code } from './filterData';
import MzQuillEditor from '@BusComponents/mz-quill-editor';
import MzImgUpload from '@components/mz-img-upload';
import doctorCategory from '@pages/doctor/doctorCategory/doctorCategory.vue';
import { normalizedDoctorCategryFormData } from '@/pages/doctor/doctorCategory/util';
export default {
    components: {
        ITitle,
        IInput,
        IForm,
        FormItem,
        ISelect,
        IOption,
        IButton,
        IRadio,
        RadioGroup,
        ISteps,
        IStep,
        MzQuillEditor,
        MzImgUpload,
        doctorCategory,
    },
    data() {
        return {
            typeOption: FilterData.typeOption,
            contentDispatch: FilterData.contentDispatch,
            title: '',
            id: this.$route.query.id,
            imgApi: ApiNr.nrUpload,
            videoApi: ApiNr.videoUpload,
            defaultQuillValue: '',
            // forceRead: false,
            formItem: {
                is_third: 0,
                title_pics: [],
                role_view: [],
                tag: '',
                garden_standard_category: '',
                content: 'test',
                is_show_bottom_btn: 0,
                bottom_btn_text: '',
                bottom_btn_link: '',
                out_view: 1,
                is_commercialization: 0,
                is_ubc: 1,
                is_share: 1,
            },
            tagMap: {},
            ruleValidate: {
                type: [
                    { required: true, message: '请选择投放位置', trigger: 'change' },
                ],
                garden_standard_category: [
                    { required: true, message: '请选择内容分发', trigger: 'change' },
                ],
                // role_view: [
                //     { required: true, type: 'array', message: '请选择角色', trigger: 'change' }
                // ],
                priority: [
                    { required: true, message: '请设置优先级', trigger: 'change' },
                ],
                description: [
                    { required: true, message: '请输入公告描述', trigger: 'change' },
                ],
                title: [
                    { required: true, message: '请填写标题', trigger: 'change' },
                ],
                is_third: [
                    { required: true, type: 'number', message: '请选择内容类型', trigger: 'change' },
                ],
                bottom_btn_text: [
                    { required: true, message: '请填写按钮文案', trigger: 'change' },
                ],
                bottom_btn_link: [
                    { required: true, message: '请填写按钮链接', trigger: 'change' },
                ],
            },
            config: [],
            externalConfig: [],
            // 医生分层
            current: 0,
            nameId: '',
            doctorCategoryData: {},

        };
    },
    async created() {
        this.title = this.id ? '编辑公告（id:' + this.id + '）' : '新增公告';
        if (this.id) {
            this.getDetail();
        }
    },
    computed: {

        showBtnConfig() {
            return this.formItem.is_show_bottom_btn === 1;
        },
        showContentDispatch() {
            return [
                doctor_list_code,
                serve_list_code,
                platform_topic_code,
                platform_list_code].includes(this.formItem.type);
        },
    },
    watch: {
        current(val) {
            if (val === 1 && this.nameId) {
                // this.getDoctorCategoryData();
            }
        },
    },
    methods: {
        getDetail() {
            this.$http.get(ApiTools.getArticleDetail, {
                params: {
                    id: this.id,
                },
            }).then(res => {
                this.formItem = res.data;
                this.nameId = res.data.name_list_id;
                this.defaultQuillValue = this.formItem.content;
            });
        },
        submitUpdate(name) {
            this.$refs[name].validate(valid => {
                if (!valid) {
                    return;
                }
                if (this.formItem.priority && this.formItem.priority > 100) {
                    this.$Message.error('当前优先级最大值：100');
                    return false;
                }
                if (!this.formItem.is_third) {
                    if (this.formItem.content) {
                        let s = this.$refs.MzQuillEditor.getContentImgResources();
                        s = JSON.stringify(s);
                        this.formItem.content = this.$refs.MzQuillEditor.getContent();

                        this.formItem.third_url = '';
                    } else {
                        this.$Message.error('请填写页面内容');
                        return false;
                    }
                } else {
                    if (this.formItem.third_url) {
                        this.formItem.content = '';
                    } else {
                        this.$Message.error('请填写外链内容');
                        return false;
                    }
                }
                // 图片传参
                const params = Object.assign({}, this.formItem);
                const picList = this.formItem.title_pics;
                if (picList && picList.length) {
                    const list = [];
                    picList.forEach(item => {
                        list.push(item.url);
                    });
                    params.title_pids = list;
                } else {
                    params.title_pids = '';
                }
                params.title_pics = '';
                this.$http.post(ApiTools.submitArticle, params, {
                    'Content-type': 'application/x-www-form-urlencoded',
                    globalTip: false,
                }).then(res => {
                    if (res && res.errno === 0) {
                        this.$Message.success({
                            content: '已发起审批，请在如流查看',
                            duration: 3.5,
                        });
                        this.nameId = res.data.name_list_id;
                        this.current = 1;
                        this.getDoctorCategoryData();
                    }
                });
            });
        },
        handleBeforeUpload() {
            if (!this.formItem.type) {
                this.$Message.error('请先选择投放位置');
                return false;
            }
        },
        uploadOk(res, file) {
            this.formItem.title_pics.push({
                url: res.data.url,
            });
        },
        removeImg(file, index) {
            this.formItem.title_pics.splice(index, 1);
        },
        quillContentchange({ html, text, quill }) {
            this.formItem.content = text;
        },
        async getDoctorCategoryData() {
            if (this.nameId) {
                const { errno, data } = await this.$http.get(ApiDoctor.namelistshow, {
                    params: {
                        list_id: this.nameId,
                    },
                });
                if (errno === 0) {
                    // 借用namelist的搜索获取详情
                    this.doctorCategoryData = normalizedDoctorCategryFormData(data.list[0]);
                }
            }
        },
        async submitDoctorCategory(params) {
            const { errno } = await this.$http.post(
                ApiDoctor.namelistconditionedit,
                params,
                {
                    headers: {
                        'Content-type':
                            'application/json',
                    },
                }
            );

            if (errno === 0) {
                this.$Message.success('目标人群选择成功');
                this.$router.push({
                    name: 'articalList',
                });
            }
        },
    },
};
</script>

<style lang="less" scoped>
   .artical-create {
       width: 760px;
       .step {
        margin: 20px 0 20px 40px;
       }
   }
</style>
