<template>
    <div class="wrap">
        <iTitle
            title="签到活动列表"
            link-url="docCheckInActivity?tab=new"
            sub-title="活动配置"
        />
        <i-spin v-if="isLoading" size="large" />
        <base-form
            ref="parient"
            :fields-data="list"
            :form-data="formData"
            :rule-validate="ruleValidate"
            :show-footer="false"
        />
        <div class="footer">
            <i-button
                v-if="$route.query.type !== 'add'"
                type="primary"
                @click="handleView"
            >预览</i-button>
            <i-button
                v-if="!isDetail"
                type="primary"
                @click="onPublish"
            >保存</i-button>
        </div>
    </div>
</template>
<script>
import Api from '@/api/tools';
import BaseForm from '../../../add/components/BaseForm';
import iTitle from '@components/mz-title';
import ImgUpload from '@components/mz-img-upload';
import DatePicker from '@components/date-picker';
import IInput from '@components/input';
import IButton from '@components/button';
import ISpin from '@components/spin';
import IRadio from '@components/radio';
import RadioGroup from '@components/radio-group';
import SearchTask from '@pages/task/growthCenter/searchTask.vue';
import dayjs from 'dayjs';

export default {
    components: {
        BaseForm,
        ISpin,
        IButton,
        iTitle,
    },
    data() {
        return {
            isLoading: false,
            isDetail: false,
            isEdit: false,
            pushDataMap: [
                {
                    label: '是',
                    value: 1,
                },
                {
                    label: '否',
                    value: 0,
                },
            ],
            pushTextMap: {
                0: '否',
                1: '是',
            },
            needSerialMap: {
                1: '连续签到',
                2: '非连续签到',
            },
            formData: {
                title: '',
                task_id: '',
                need_serial: '1',
                bg_img: '',
                rule_bg_img: '',
                rule_router: '',
                content: '',
                list_id: '',
                is_push: 0,
                push_content: '',
                activity_date: [],
            },
            ruleValidate: {
                title: {
                    required: true,
                    message: '活动标题不能为空',
                },
                bg_img: {
                    required: true,
                    message: '背景图片不能为空',
                },
                rule_bg_img: {
                    required: true,
                    message: '规则按钮图片不能为空',
                },
                rule_router: {
                    required: true,
                    message: '规则按钮链接不能为空',
                },
                list_id: {
                    required: false,
                    message: '活动成员名单不能为空',
                    trigger: 'blur',
                },
                content: {
                    required: true,
                    message: '活动说明不能为空',
                },
                is_push: {
                    required: true,
                    message: '请选择是否push',
                },
                push_content: {
                    required: true,
                    message: 'push文案不能为空',
                },
                activity_date: {
                    required: true,
                    validator: (rule, value, callback) => {
                        if (!value[0]) {
                            callback(new Error('活动时间不能为空'));
                        }
                        callback();
                    },
                },
            },
            list: [],
            treeList: [
                {
                    key: 'task_id',
                    label: '任务ID',
                    type: 'extra',
                    render: h => (
                        <div>
                            <SearchTask v-model={this.formData.task_id} status={0} />
                        </div>
                    ),
                },
                {
                    key: 'title',
                    label: '活动标题',
                    type: 'extra',
                    render: h => (
                        <div>
                            {
                                this.isDetail ? (<span>{this.formData.title}</span>) : (
                                    <IInput v-model={this.formData.title} placeholder="请输入活动标题"/>
                                )
                            }
                        </div>
                    ),
                },
                {
                    key: 'bg_img',
                    label: '背景图片',
                    type: 'extra',
                    render: h => (
                        <div class="form-one-row">
                            <ImgUpload
                                    class="img"
                                    name="bg_img"
                                    data={{ pic_type: 1 }}
                                    multiple={true}
                                    action="/mzmis/common/newpicupload"
                                    defaultList={((this.formData.bg_img
                                        && [this.formData.bg_img]) || []).map(el => ({
                                        url: el,
                                    }))}
                                    disabled={this.isDetail}
                                    countLimit={1}
                                    max-size={512}
                                    onUploadSuccess={this.uploadSuccess}
                                    onDel={this.handleImgRemove}
                                >
                            </ImgUpload>
                        </div>
                    ),
                },
                {
                    key: 'rule_bg_img',
                    label: '规则按钮图',
                    type: 'extra',
                    render: h => (
                        <div class="form-one-row">
                            <ImgUpload
                                    class="img"
                                    name="rule_bg_img"
                                    data={{ pic_type: 1 }}
                                    multiple={true}
                                    action="/mzmis/common/newpicupload"
                                    defaultList={((this.formData.rule_bg_img
                                        && [this.formData.rule_bg_img]) || []).map(el => ({
                                        url: el,
                                    }))}
                                    disabled={this.isDetail}
                                    countLimit={1}
                                    max-size={512}
                                    onUploadSuccess={this.uploadSuccess}
                                    onDel={this.handleImgRemove}
                                >
                            </ImgUpload>
                        </div>
                    ),
                },
                {
                    key: 'rule_router',
                    label: '规则按钮跳转链接',
                    type: 'extra',
                    render: h => (
                        <div>
                            {
                                this.isDetail ? (<span>{this.formData.rule_router}</span>) : (
                                    <IInput
                                        v-model={this.formData.rule_router}
                                        maxlength={150}
                                        placeholder="请输入规则按钮跳转链接(最长150个字)"
                                    />
                                )
                            }
                        </div>
                    ),
                },
                {
                    key: 'list_id',
                    label: '活动成员名单',
                    type: 'extra',
                    render: h => (
                        <div>
                            {
                                this.isDetail ? (<span>{this.formData.list_id}</span>) : (
                                    <IInput v-model={this.formData.list_id} placeholder="请输入活动成员名单List"/>
                                )
                            }
                        </div>
                    ),
                },
                {
                    key: 'content',
                    label: '活动说明',
                    type: 'extra',
                    render: h => (
                        <div>
                            {
                                this.isDetail ? (<span>{this.formData.content}</span>) : (
                                    <IInput
                                        type="textarea" v-model={this.formData.content}
                                        placeholder="请输入活动说明(最长200个字)"
                                        maxlength="200"
                                    >
                                    </IInput>
                                )
                            }
                        </div>
                    ),
                },
                {
                    key: 'activity_date',
                    label: '活动时间',
                    type: 'extra',
                    render: h => (
                        <DatePicker
                            v-model={this.formData.activity_date}
                            type="datetimerange"
                            placeholder="请选择活动日期"
                            style="width: 320px"
                            options={this.dateOptions}
                        ></DatePicker>
                    ),
                },
                {
                    key: 'is_push',
                    label: '是否需要push',
                    type: 'extra',
                    render: h => (
                        <div>
                            {
                                this.isDetail ? (<span>{this.pushTextMap[this.formData.is_push]}</span>) : (
                                    <i-select
                                        v-model={this.formData.is_push}
                                        placeholder="是否push"
                                        style="width: 250px"
                                    >
                                        {
                                            this.pushDataMap.map(item => (
                                                <i-option
                                                    key={item.value}
                                                    label={item.label}
                                                    value={item.value}
                                                />
                                            ))
                                        }
                                    </i-select>
                                )
                            }
                        </div>
                    ),
                },
                {
                    key: 'need_serial',
                    label: '签到要求',
                    type: 'extra',
                    render: h => (
                        <div>
                            <RadioGroup v-model={this.formData.need_serial}>
                                {Object.keys(this.needSerialMap).map(item => {
                                    return (<IRadio key={item} label={item}>{this.needSerialMap[item]}</IRadio>);
                                    })}
                            </RadioGroup>
                        </div>
                    ),
                },
            ],
            pushList: [
                {
                    key: 'push_content',
                    label: 'push内容',
                    type: 'extra',
                    render: h => (
                        <div>
                            {
                                this.isDetail ? (<span>{this.formData.push_content}</span>) : (
                                    <IInput
                                        v-model={this.formData.push_content}
                                        maxlength={10} placeholder="请输入push文案（最多10个字）"
                                    />
                                )
                            }
                        </div>
                    ),
                },
            ],
            dateOptions: {
                disabledDate: date => {
                    return date && date.valueOf() < Date.now();
                },
            },
            gap: 1000,
        };
    },
    methods: {

        // 保存活动配置
        async onPublish() {
            const { activity_date } = this.formData;
            const params = {
                ...this.formData,
            };
            if (activity_date.length > 0 && activity_date[0]) {
                params.start_time = +new Date(activity_date[0]) / this.gap;
                params.end_time = +new Date(activity_date[1]) / this.gap;
            }

            if (this.isEdit) {
                params.id = this.$route.query.id;
            }

            // 表单校验
            await new Promise((resolve, reject) => {
                this.$refs.parient.$refs.ruleValidate
                    .validate(valid => {
                        if (valid) {
                            resolve();
                        } else {
                            reject();
                        }
                    });
            });
            params.bg_img = params.bg_img?.match(/\/(\d+)$/)[1];
            params.rule_bg_img = params.rule_bg_img?.match(/\/(\d+)$/)[1];
            const { errno } = await this.$http.post(
                this.isEdit ? Api.newsignupedit : Api.newsignupadd,
                {
                    ...params,
                }, {
                    headers: {
                        'Content-type': 'application/x-www-form-urlencoded',
                    },
            });

            if (errno === 0) {
                this.$Message.success(this.isEdit ? '活动编辑成功' : '活动已保存');
                this.$router.push('docCheckInActivity?tab=new');
            };
        },
        async getDetail() {
            const { type, id } = this.$route.query;
            this.isLoading = true;

            type === 'edit' ? this.isEdit = true : this.isDetail = true;

            const { errno, data: { list: [data] } } = await this.$http.get(Api.newsignuplist, {
                params: {
                    id,
                },
            });
            if (errno === 0) {
                this.formData = {
                    ...data,
                    list_id: data.list_id?.toString(),
                    activity_date: [
                        dayjs(data.start_time),
                        dayjs(data.end_time)],
                };
                this.isLoading = false;
            }
        },
        uploadSuccess({ data: { url } }, file, name) {
            this.formData[name] = url;
        },
        handleImgRemove(file, index, name) {
            this.formData[name] = '';
        },
        handleView() {
            const { url } = this.formData;

            if (!url) {
                this.$Message.error('无法获取到预览链接');
                return;
            }
            window.open(url);
        },
    },
    watch: {
        'formData.is_push': {
            handler(newVal, oldVal) {
                if (newVal === 1) {
                    this.list = [...this.treeList, ...this.pushList];
                } else {
                    this.list = this.treeList;
                }
            },
            deep: true,
            immediate: true,
        },
    },
    created() {
        const { type } = this.$route.query;
        if (type === 'edit' || type === 'detail') {
            this.getDetail();
        }
    },
};
</script>
<style lang="less" scoped>
.wrap {
    .ivu-spin {
        position: absolute;
        top: 200px;
        left: 300px;
    }
}
.footer  {
    margin-left: 50px;
}
/deep/ .one-row {
    display: flex;

    span {
        width: 100px;
    }
}
/deep/ .can_click {
    color: #5183ff;

    &:hover {
        cursor: pointer;
    }
}
/deep/ .step {
    display: flex;
    width: 600px;
    margin-bottom: 5px;
}
/deep/ .money_right {
    display: flex;
}
/deep/ .text {
    display: inline-block;
    width: 68px;
    flex: none;
}
/deep/ .extra {
    margin-left: 5px;
}
/deep/ .sub{
    position: relative;
    font-size: 28px;
    color: red;
    margin-top: 5px;
    margin-left: 4px;
    &:hover {
        cursor: pointer;
    }
}
</style>
