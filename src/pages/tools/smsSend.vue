<template>
    <div class="artical-create">
        <i-title
            title="短信发送记录" sub-title="短信发送"
            link-url="smsList"
        />
        <i-form
            :model="formItem"
            :label-width="100"
            class="inner-box c-gap-left-large"
        >
            <form-item label="发送方式：" prop="uid">
                <radio-group v-model="formItem.entityType" @on-change="clearItem">
                    <i-radio
                        v-for="(val, key) in entityConfig"
                        :key="key"
                        :label="key"
                    >{{ val }}</i-radio>
                </radio-group>
                <div v-show="isUidOrPhone">
                    <div>
                        <i-select
                            v-model="formItem.sourceType"
                            class="c-gap-bottom c-gap-top-small"
                            style="width:200px"
                            @on-change="clearItem"
                        >
                            <i-option
                                v-for="(val, key) in sourceConfig"
                                :key="key"
                                :label="val"
                                :value="key"
                            />
                        </i-select>
                    </div>
                    <i-input
                        v-show="formItem.sourceType === '1'"
                        v-model="formItem.entityContent" type="textarea"
                        :rows="4"
                        :placeholder="formItem.entityType === '1' ? uidPlaceholderText : phonePlaceholderText"
                        @on-focus="clearInfor"
                    />
                    <i-upload
                        v-show="formItem.sourceType === '2' && !formItem.fileName"
                        class="mz-img-upload c-inline-block"
                        action="#"
                        accept=".csv"
                        :max-size="1048576"
                        :before-upload="beforeUpload"
                    >
                        <div class="upload-input">
                            <i-icon type="ios-cloud-upload" size="25" />
                        </div>
                    </i-upload>

                    <p
                        v-show="!!formItem.fileName && formItem.sourceType === '2'" class="c-gap-left-small"
                        flex="cross:center"
                    >
                        <span style="color:rgb(73, 221, 247)">{{ formItem.fileName }}</span>
                        <i-icon
                            class="c-gap-left"
                            color="red"
                            size="20"
                            type="ios-close"
                            style="cursor:pointer"
                            @click="removeFile"
                        />
                    </p>
                    <p v-show="formItem.sourceType === '2'">
                        * 仅支持csv格式文件，一个{{ formItem.entityType === '1' ? 'UID' : '手机号' }}单独一行
                    </p>
                </div>
            </form-item>
            <form-item
                v-show="!isUidOrPhone" prop="docGroupId"
                label="名单圈选："
            >
                <mz-name-list-search
                    v-model="formItem.docGroupId"
                    placeholder="搜索名单id/名单名称"
                />
            </form-item>
            <form-item label="短信模板：" prop="template_id">
                <radio-group v-model="formItem.template_id" @on-change="clearInfor">
                    <i-radio
                        v-for="(text, val) in templateConfig"
                        :key="val"
                        :label="val"
                    >
                        <span>{{ text }}</span>
                    </i-radio>
                </radio-group>
            </form-item>
            <form-item label="短信签名：" prop="sign">
                <radio-group v-model="formItem.sign" @on-change="clearInfor">
                    <i-radio :label="3">
                        <span>百度健康</span>
                    </i-radio>
                </radio-group>
            </form-item>
            <form-item label="短信内容：" prop="content">
                <i-input
                    v-model="formItem.content" type="textarea"
                    :rows="4" placeholder="您好，本月绩效已发放，请查收"
                    @on-focus="clearInfor"
                />
                <p>注意：短信内容不能包含全角和半角中括号以及制表符([]【】)</p>
                <p style="font-weight: 600">{{ isSendInformation ? '短信已发送' : '' }}</p>
            </form-item>

            <form-item label="">
                <i-button
                    class="c-gap-right-small"
                    type="success"
                    shape="circle"
                    @click="submit('formItem')"
                >
                    提交
                </i-button>
            </form-item>
        </i-form>
    </div>
</template>

<script>
    import ApiTools from '@/api/tools';
    import Api from '@/api/nr';
    import axios from 'axios';
    import ITitle from '@components/mz-title';
    import IInput from '@components/input';
    import IForm from '@components/form';
    import FormItem from '@components/form-item';
    import IRadio from '@components/radio';
    import RadioGroup from '@components/radio-group';
    import IButton from '@components/button';
    import Util from '@/utils/util';
    import IUpload from '@components/upload';
    import MzNameListSearch from '@BusComponents/mz-name-list-search/index.vue';
    import { uploadBos } from '@/utils/uploadBoss';

    export default {
        components: {
            ITitle,
            IInput,
            IForm,
            FormItem,
            IButton,
            IRadio,
            RadioGroup,
            IUpload,
            MzNameListSearch,
        },
        data() {
            this.sourceConfig = {
                '1': '手动输入',
                '2': '上传文件',
            };
            this.entityConfig = {
                '1': '按uid发送',
                '2': '按手机号发送',
                '3': '名单圈选',
            };
            this.templateConfig = {
                'doctor_serve': '医生-服务类通知',
                'doctor_marketing': '医生-营销类通知',
                'patient_serve': '患者-服务类通知',
                'patient_marketing': '患者-营销类通知',
            };
            return {
                isSendInformation: false,
                uidPlaceholderText: '请输入UID，多个UID用英文逗号隔开，最多支持输入2000个UID；例:**********,**********',
                phonePlaceholderText: '请输入手机号，多个手机号用英文逗号隔开，最多支持输入2000个号码；例:18601034180;18601038888',
                formItem: {
                    smsSign: '',
                    content: '',
                    entityType: '1',
                    sourceType: '1',
                    entityContent: '',
                    fileName: '',
                    bosObjKey: '',
                    docGroupId: null,
                },
                smsTplMap: {},
                statusMap: {},
            };
        },
        created() {
            this.getConfig();
        },
        computed: {
            isUidOrPhone() {
                return this.formItem.entityType === '1' || this.formItem.entityType === '2';
            },
        },
        watch: {

        },
        methods: {
            async getConfig() {
                const { data, errno } = await this.$http.post(ApiTools.getMsgConfig);

                if (errno === 0) {
                    this.smsTplMap = data.sms_tpl;
                    this.statusMap = data.sms_status;
                }
            },
            removeFile() {
                this.formItem.bosObjKey = '';
                this.formItem.fileName = '';
            },
            beforeUpload(file) {
                this.handleBosUpload(file);
                return false;
            },
            async handleBosUpload(file) {
                try {
                    const { order_id } = await uploadBos(file);
                    if (order_id) {
                        this.formItem.bosObjKey = order_id;
                        this.formItem.fileName = file.name;
                    };
                } catch {
                    this.$Message.error('上传失败, 请重试');
                }
            },
            clearItem() {
                this.formItem.fileName = '';
                this.formItem.bosObjKey = '';
                this.formItem.entityContent = '';
                this.clearInfor();
                this.formItem.docGroupId = null;
            },
            clearInfor() {
                if (this.isSendInformation) {
                    this.isSendInformation = false;
                }
            },
            async submit(name) {
                if (!this.isUidOrPhone && !this.formItem.docGroupId) {
                    this.$Message.error('请选择名单后再提交');
                    return;
                }
                const params = {
                    ...this.formItem,
                    entityType: this.isUidOrPhone ? this.formItem.entityType : 1,
                    sourceType: this.isUidOrPhone ? this.formItem.sourceType : this.formItem.entityType,
                };
                const res = await this.$http.post(ApiTools.smssend, params, {
                    'Content-type': 'application/x-www-form-urlencoded',
                });

                if (res.errno === 0) {
                    this.$Message.success({
                        content: '已发起审批，请在如流查看',
                        duration: 3.5,
                    });
                    this.isSendInformation = true;
                    this.$router.push('/bOperator/smsList');
                }
            },
        },
    };
</script>

<style lang="less" scoped>

</style>
