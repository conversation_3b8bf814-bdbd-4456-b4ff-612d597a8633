<template>
    <div class="doctor-reg">
        <i-title title="医生执业登记列表" />
        <i-tabs :value="tabValue" @on-click="changeTab">
            <i-tab-pane label="海南医来互联网医院" name="1" />
            <i-tab-pane label="银川百度健康互联网医院" name="2" />
        </i-tabs>
        <div class="search-container c-gap-bottom">
            <base-search
                class="header" :search-list="searchList"
                :search-fields="searchFields"
                @onSubmit="() => getList(1)"
            >
                <template #export>
                    <i-button
                        class="c-gap-right-small" type="info"
                        shape="circle" @click="exportExcel"
                    >导出</i-button>
                </template>
            </base-search>
        </div>
        <base-table
            class="addTable" :loading="loading"
            :columns="tableColumns" :data-source="dataSource"
            :pagination="pagination"
        />
    </div>
</template>
<script lang="tsx" setup>
import BaseSearch from '@pages/add/components/BaseSearch/index.vue';
import Api from '@/api/yilai';
import { statusConf as backStatusConf } from '../regFormData';
import util from '@/utils/util';
import { reactive, ref } from 'vue';
import { useRouter } from '@/hooks/router';
import { getRegList, getConfig, reapplyAudit } from './api';
import Message from '@components/message';
import dayjs from 'dayjs';
import MzDatePicker from '@pages/BusComponents/mz-datePicker/index.vue';



const auditStatusConfig = [
    { label: '待审核', value: 0, params: { status: 0, submit_status: 1 } },
    { label: '待提交', value: 1, params: { status: 1, submit_status: 1 } },
    { label: '已提交', value: 2, params: { status: 1, submit_status: 2 } },
    { label: '已关闭', value: 3, params: { status: 2, submit_status: '' } },
];
const statusConf = ref<any>({});
const searchList = reactive({
    uid: '',
    name: '',
    role: '',
    status: null,
    submit_status: '',
    update_at_range: '',
});
const searchFields = [
    {
        key: 'uid',
        type: 'input',
        placeholder: 'UID',
    },
    {
        key: 'name',
        type: 'input',
        placeholder: '医生姓名',
    },
    {
        type: 'extra',
        render: h => (
            <i-select
                value={searchList.role}
                placeholder="医师类型"
                clearable
                style="width: 200px"
                onOn-change={value => (searchList.role = value)}
            >
                {statusConf.value.role && Object.keys(statusConf.value.role).map(item => (
                    <i-option
                        key={item}
                        value={item}
                        label={statusConf.value.role[item]}
                    />
                ))}
            </i-select>
        ),
    },
    {
        key: 'status',
        type: 'select',
        placeholder: '状态',
        optionList: auditStatusConfig,
    },
    {
        key: 'timerange',
        type: 'timerange',
        render: h => (
            <MzDatePicker format="yyyy-MM-dd HH:mm:ss" placeholder="更新时间" style="width: 320px"
                onOn-change={
                    dateString => {
                        if (dateString) {
                            searchList.update_at_range = dayjs(dateString[0]).unix()
                                                             + '-' + dayjs(dateString[1]).unix();
                        }
                        else {
                            searchList.update_at_range = '';
                        }


                    }
                }
            />
        ),
    },
];

const router = useRouter();
const tabValue = ref('1');
const loading = ref(false);
const dataSource = ref([]);
const pagination = reactive({
    tablePagination: {
        pageSize: 20,
        pageIndex: 1,
        total: 0,
    },
    onChange: data => {
        pagination.tablePagination.pageIndex = data;
        getList();
    },
});
const statusObj = {
    0: '待审核',
    1: '通过',
    2: '驳回',
};
const tableColumns = [
    {
        title: '序号',
        key: 'id',
        align: 'center',
        minWidth: 70,
    },
    {
        title: 'UID',
        key: 'uid',
        align: 'center',
        minWidth: 140,
    },
    {
        title: '医师类型',
        key: 'doctor_type',
        align: 'center',
        minWidth: 100,
    },
    {
        title: '姓名',
        key: 'name',
        align: 'center',
        minWidth: 100,
    },
    {
        title: '医院',
        key: 'company',
        align: 'center',
        minWidth: 180,
    },
    {
        title: '科室',
        key: 'doc_dept',
        align: 'center',
        minWidth: 100,
    },
    {
        title: '审核状态',
        key: 'status',
        align: 'center',
        minWidth: 100,
        render: (h, params) => {
            return h(
                'span',
                statusObj[params.row.status] || ''
            );
        },
    },
    {
        title: '添加时间',
        key: 'create_at',
        align: 'center',
        minWidth: 180,
        render(h, params) {
            return h(
                'span',
                params.row.create_at ? util.seconds2time(params.row.create_at) : ''
            );
        },
    },
    {
        title: '更新时间',
        key: 'update_at',
        align: 'center',
        minWidth: 180,
        render(h, params) {
            return h(
                'span',
                params.row.update_at ? util.seconds2time(params.row.update_at) : ''
            );
        },
    },
    {
        title: '操作人',
        key: 'op_name',
        align: 'center',
        minWidth: 130,
    },
    {
        title: '操作',
        key: '-',
        align: 'center',
        minWidth: 180,
        fixed: 'right',
        render: (h, params) => {
            const style = {
                'margin-right': '10px',
            };
            const detail = h(
                'a',
                {
                    on: {
                        click: () => {
                            jump(
                                params.row.id,
                                tabValue.value,
                                'detail',
                                'reReview'
                            );
                        },
                    },
                    style: style,
                },
                '查看'
            );
            const audit = h(
                'a',
                {
                    on: {
                        click: () => {
                            jump(
                                params.row.id,
                                tabValue.value,
                                'audit',
                                'reReview'
                            );
                        },
                    },
                    style: style,
                },
                '审核'
            );
            const butsMap = new Map([
                [0, [detail, audit]],
                [1, [detail]],
                [2, [detail]],
            ]);
            const status = Number(params.row.status);
            const submitStatus = Number(params.row.submit_status);
            // 待审核
            const pending_audit = status === 0 && submitStatus === 1;
            // 待提交
            const pending_submit = status === 1 && submitStatus === 1;
            // 已提交
            const success_submit = status === 1 && submitStatus === 2;
            // 终止
            const stop_submit = status === 2 && (submitStatus === 1 || submitStatus === 2);
            let result = [];
            if (pending_audit) {
                result = butsMap.get(0);
            } else if (pending_submit) {
                result = butsMap.get(1);
            } else if (success_submit || stop_submit) {
                result = butsMap.get(2);
            };
            return h(
                'div',
                result
            );
        },
    },
];

const changeTab = type => {
    tabValue.value = type;
    getList();
};

const getStatusConf = async () => {
    const { data, errno } = await getConfig();
    if (errno === 0) {
        statusConf.value = {
            role: data.roles,
            status: data.status,
            ywx_status: data.ywx_status,
            record_status: data.record_status,
            cids: data.cids,
            submit_status: backStatusConf.submit_status,
        };
    } else {
        statusConf.value = Object.freeze(backStatusConf);
    }
};

const getList = async (index = 0) => {
    loading.value = true;
    if (index) {
        pagination.tablePagination.pageIndex = index;
    }
    const params = {
        ...searchList,
        ...(auditStatusConfig[searchList.status]?.params || {}),
        uid: searchList.uid.replace(/\s/g, ''),
        org_id: tabValue.value,
        pn: pagination.tablePagination.pageIndex,
        rn: pagination.tablePagination.pageSize,
    };
    const { data: { list, total } } = await getRegList(params);
    pagination.tablePagination.total = total || 0;
    dataSource.value = list || [];
    loading.value = false;
};

function jump(edit_id, type, jumpType, auditType) {
    router.push({
        path: '/internetHospital/doctorReg',
        query: {
            edit_id,
            type,
            jumpType,
            auditType,
        },
    });
};

const onSubmit = async (id, op_type) => {
    const { errno } = await reapplyAudit({ id: id, op_type });
    if (errno === 0) {
        Message.info('操作成功！');
        getList();
    }
};

const exportExcel = () => {
    const newParams = { ...searchList };
    auditStatusConfig.forEach(item => {
        if (item.value === newParams.status) {
            newParams.status = item.params?.status;
            newParams.submit_status = item.params?.submit_status + '';
        }
    });

    Object.entries(newParams).forEach(([key, value]) => {
        if (!value && value !== 0) {
            delete newParams[key];
        }
    });

    util.openNewUrl(Api.staffreapplyexport, { ...newParams, org_id: tabValue.value });

};

getList(1);
getStatusConf();
</script>
<style lang="less" scoped>
.search-container {
    display: flex;
    flex-wrap: wrap;

    &>div {
        margin-right: 14px;
        margin-bottom: 14px;
    }
}
</style>
