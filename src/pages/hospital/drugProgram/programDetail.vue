<template>
    <div>
        <mz-title
            title="方案详情"
            :sub-title="class_name"
            :link-url="{name: 'drugProgramManager'}"
        />
        <h4 class="c-gap-top">注意：条形码和通用名需要和商城后台配置完全一致！</h4>
        <p class="wrap-btn">
            <i-button
                class="c-gap-right"
                type="primary"
                icon="md-add"
                @click="openAddProgram"
            >新建</i-button>
            <i-button
                type="success"
                @click="onSubmitTemp"
            >提交设置</i-button>
        </p>
        <div class="wrap-box">
            <i-spin v-if="pageInfo.loading" fix />
            <draggable
                v-model="pageInfo.list"
                :options="dragOptions"
                :animation="100"
                scroll-sensitivity="200"
                @start="() => onDragHandle(true)"
                @end="() => onDragHandle(false)"
            >
                <div
                    v-for="(program, i) in pageInfo.list"
                    :key="program.drug_plan_id"
                    :name="i"
                    class="c-gap-top program-wrap"
                >
                    <div class="program-wrap-title c-gap-bottom-small">
                        <div class="program-wrap-title-text">
                            <span>{{ program?.drug_plan_name }} ，</span>
                            <span>方案id：{{ program.drug_plan_id || ' - ' }} ，</span>
                            <span>
                                {{ isPres ? '诊断' : '疾病标签' }}：
                                {{ (program?.disease_list || []).map(item => item.disease_name).join('｜') || ' - ' }}，
                            </span>
                            <span>适应症：{{ program?.indication }}</span>
                        </div>
                        <div class="program-wrap-title-operate">
                            <a
                                class="c-gap-right-small"
                                @click="() => openEditProgram(program)"
                            >编辑</a>
                            <a
                                class="c-gap-right-small"
                                @click="() => openAddDrug(program.drug_list)"
                            >添加</a>
                            <a class="c-gap-right" @click="() => onDelProgram(i)">删除</a>
                            <i-icon
                                class="dragger-icon"
                                type="md-apps"
                                style="cursor: grab"
                            />
                        </div>
                    </div>
                    <i-table
                        :columns="tableColumns"
                        :data="program?.drug_list"
                        height="210"
                        :draggable="!isOutDrug"
                        border
                        @on-drag-drop="(currI, tarI) => dragDrop(currI, tarI, program?.drug_list, i)"
                    >
                        <template slot="action" slot-scope="{row}">
                            <a class="c-gap-right-small" @click="() => openEditDrug(row, i)">编辑</a>
                            <a class="c-gap-right-small" @click="() => onDelDrug(row, i)">删除</a>
                        </template>
                    </i-table>
                </div>
            </draggable>
            <mz-quill-editor
                class="quill"
                ref="QuillRef"
                :show-text-count="true"
                :img-upload-url="ApiNr.nrUpload"
                :video-upload-url="ApiNr.videoUpload"
                :value="pageInfo.explanation"
                :config="toolbarConfig"
            />
        </div>
        <drug-modal
            ref="drugHandler"
            @add="onAddDrug"
            @edit="onEditDrug"
        />
        <program-modal
            ref="programHandler"
            :is-pres="isPres"
            @add="onAddProgram"
            @edit="onEditProgram"
        />
    </div>
</template>
<script lang="tsx" setup>
import draggable from 'vuedraggable';
import { ref, reactive, onMounted, computed } from 'vue';

import Modal from '@components/modal';
import Message from '@components/message';
import MzTitle from '@components/mz-title';
import MzQuillEditor from '@pages/BusComponents/mz-quill-editor';

import ApiNr from '@/api/nr';
import { useRoute } from '@/hooks/router';
import { formatHtml } from '@/utils/util';

import { getTmpl, submitTmpl } from './api';
import drugModal from './drugModal.vue';
import programModal from './programModal.vue';

const toolbarConfig = {
    modules: {
        toolbar: [
            {
                'header': [1, 2, 3, false],
            },
            {
                'color': [],
            },
            {
                'background': [],
            },
            'bold',
            'italic',
            'underline',
            'image',
            'video',
            {
                'list': 'ordered',
            },
            {
                'list': 'bullet',
            },
            'link',
        ],
    },
};

const QuillRef = ref(null);

const LIMIT_PROGRAM = 6; // 方案数量
const { query: { class_name, class_id, type } } = useRoute();
// 处方方案
const isPres = computed(() => +type === 2);

const pageInfo = reactive({
    list: [],
    loading: false,
    explanation: '',
});

const dragOptions = {
    handle: '.dragger-icon',
};

const isOutDrug = ref(false);
const onDragHandle = dragStatus => {
    isOutDrug.value = dragStatus;
};


const tableColumns = [
    {
        key: 'sort',
        title: '商品排序',
        align: 'center',
        render: (h, { row }) => {
              return (
                  <i-icon type="md-apps" style={{ cursor: 'grab' }}/>
              );
          },
    },
    {
        key: 'barcode',
        title: '商品条形码',
        align: 'center',
    },
    {
        key: 'common_name',
        title: '通用名',
        align: 'center',
    },
    {
        key: 'spec',
        title: '规格',
        align: 'center',
    },
    {
        key: 'signetur',
        title: '用法用量',
        align: 'center',
    },
    {
        key: 'amount',
        title: '商品数量',
        align: 'center',
    },
    {
        title: '操作',
        slot: 'action',
        align: 'center',
    },
];

const programHandler = ref(null);

const openAddProgram = () => {
    programHandler.value.open('add');
};

let programBridge = {
    drug_plan_name: '',
    indication: '',
    disease_list: [],
};

const openEditProgram = data => {
    programBridge = data;
    programHandler.value.open('edit', data);
};

const onAddProgram = data => {
    pageInfo.list.push({
        drug_plan_id: 0,
        ...data,
    });
};

const onEditProgram = ({ drug_plan_name, indication, disease_list }) => {
    programBridge.drug_plan_name = drug_plan_name;
    programBridge.indication = indication;
    programBridge.disease_list = disease_list;
};

const onDelProgram = (i: number) => {
  Modal.confirm({
      title: '确认删除该方案吗？',
      onOk: async () => {
        pageInfo.list.splice(i, 1);
      },
  });
};

// 药品
const drugHandler = ref(null);
const drugBridge = {
    programIndex: null,
    drugRow: null,
    drugIndex: null,
    allDrugInfo: [],
};


const openAddDrug = drug_list => {
    if (drug_list?.length >= LIMIT_PROGRAM) {
        Message.error('最多创建' + LIMIT_PROGRAM + '个');
        return;
    };
    drugHandler.value.open('add');
    drugBridge.allDrugInfo = drug_list;
};

const openEditDrug = (row, i) => {
    const { _index, _rowKey, ...data } = row;
    drugBridge.programIndex = i;
    drugBridge.drugIndex = _index;
    drugBridge.drugRow = row;
    drugHandler.value.open('edit', data);
};

const onAddDrug = drugFormData => {
    drugBridge.allDrugInfo.push(drugFormData);
};
const onEditDrug = drugFormData => {
    const { programIndex, drugIndex, drugRow } = drugBridge;
    pageInfo.list[programIndex].drug_list[drugIndex] = { ...drugFormData };
    Object.keys(drugFormData).forEach(item => {
        drugRow[item] = drugFormData[item];
    });
};

const dragDrop = (currentIndex, targetIndex, drug_list, i) => {
    const tmpList = drug_list.slice();
    if (!tmpList[targetIndex]) {
        return;
    };
    if (currentIndex !== targetIndex) {
        const temp = tmpList[currentIndex];
        tmpList.splice(currentIndex, 1, tmpList[targetIndex]);
        tmpList.splice(targetIndex, 1, temp);
        pageInfo.list[i].drug_list = tmpList;
    }
};

const onDelDrug = ({ _index }, i: number) => {
  Modal.confirm({
      title: '确认删除该商品吗？',
      onOk: async () => {
        pageInfo.list[i].drug_list.splice(_index, 1);
      },
  });
};

const getDetail = async () => {
    pageInfo.loading = true;
    const { errno, data } = await getTmpl({
        class_id: +class_id,
    });

    if (errno === 0) {
        pageInfo.explanation = data.explanation;
        pageInfo.list = data.list || [];
    };
    pageInfo.loading = false;
};

const onSubmitTemp = async () => {
    try {

        const textCount = QuillRef.value?.textCount;
        const quillValue =  QuillRef.value?.getContent();

        if (textCount > 3000) {
            Message.error('内容最多3000字！');
            return;
        };

        if (!pageInfo.list || !pageInfo.list.length) {
            Message.error('方案不能为空');
            return;
        };

        pageInfo.list.forEach((el, index) => {
            el.sort = index + 1;
        });
        pageInfo.loading = true;
        const { errno } = await submitTmpl({
            class_id: +class_id,
            detail: pageInfo.list,
            explanation: formatHtml(quillValue) || '', // 过滤掉富文本class样式,因为在c端class不生效
        });

        if (errno === 0) {
            Message.success('已实时更新');
            window.location.reload();
        };
    } finally {
        pageInfo.loading = false;
    }
};

onMounted(() => {
    getDetail();
});
</script>
<style scoped lang='less'>
.wrap-btn{
    text-align: right;
}
.input-text {
    width: 300px;
}
.wrap-box{
  position: relative;
  .program-wrap{
      padding: 10px;
      border: 1px solid #d3d3d3;
      &-title{
          color: #515a6e;
          font-size: 14px;
          line-height: 20px;
          background-color: #f8f8f9;
          font-weight: bold;
          padding: 10px 20px;
          position: relative;
          &-operate{
              height:40px;
              line-height:40px;
              position: absolute;
              right: 20px;
              top: 0;
          }
          &-text{
            padding-right: 200px;
          }
      }
  }
}

.quill{
    margin-top: 20px;
}
</style>
