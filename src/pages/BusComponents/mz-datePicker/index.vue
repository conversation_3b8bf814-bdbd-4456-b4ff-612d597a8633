<template>
    <DatePicker
        v-model="modelValue"
        type="datetimerange"
        :format="format"
        :placeholder="placeholder"
        @on-change="handleDateChange"
    />
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import { ref, watch } from 'vue';

// 定义组件Props类型
interface Props {
    modelValue?: [string, string] | null;
    format?: string;
    placeholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
    format: 'yyyy-MM-dd HH:mm',
    placeholder: () => '请选择时间',
});

// 定义emit类型
const emit = defineEmits<{
    (event: 'update:modelValue', value: [string, string] | null): void;
    (event: 'on-change', value: [string, string] | null): void;
}>();

// 响应式绑定值
const modelValue = ref<[string, string] | null>(props.modelValue);

// 监听父组件传递的modelValue变化
watch(
    () => props.modelValue,
    newVal => {
        modelValue.value = newVal;
    }
);

let oldValue = null;
watch(
    () => modelValue.value,
    (newVal, oldVal) => {
        oldValue = oldVal;
    }
);

// 日期范围变化处理
const handleDateChange = (dates: [string, string]) => {
    // 如果没有选择时间，则清空绑定值
    if (!dates || dates.length < 2 || dates[0].toString()?.length === 0) {
        emit('update:modelValue', null);
        emit('on-change', null);
        return;
    }

    // 日期不变 可以强制把结束时间修改为 00:00:00
    if (
        dayjs(oldValue?.[0]).format('yyyy-MM-dd')
            === dayjs(dates?.[0]).format('yyyy-MM-dd')
        && dayjs(oldValue?.[1]).format('yyyy-MM-dd')
            === dayjs(dates?.[1]).format('yyyy-MM-dd')
    ) {
        emit('update:modelValue', dates);
        emit('on-change', dates);
        return;
    }

    if (dayjs(dates?.[1]).format('HH:mm:ss') === '00:00:00') {
        const [start, end] = dates;
        const adjustedDates: [string, string] = [
            start,
            dayjs(end).endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        ];
        // 更新绑定值
        modelValue.value = [...adjustedDates];
        // 通知父组件
        emit('update:modelValue', adjustedDates);
        emit('on-change', adjustedDates);
    } else {
        emit('update:modelValue', dates);
        emit('on-change', dates);
    }
};
</script>
