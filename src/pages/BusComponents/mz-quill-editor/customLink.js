import Quill from './quill/quill.min.js';
const Link = Quill.import('formats/link');
class CustomLink extends Link {
    static create(value) {
        const node = super.create(value);
        if (CustomLink.sanitize(value)) {
            node.setAttribute('href', value);
        }
        return node;
    }

    static sanitize(url) {
    // 使用原始的 sanitize 方法并添加自定义协议
        const pattern = /^((http|https|mailto|tel|mzdoctor):\/\/)/i;
        return pattern.test(url);
    }
}
export default CustomLink;
