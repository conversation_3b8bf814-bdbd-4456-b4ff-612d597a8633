<!-- 互斥树选择组件 -->
<template>
    <i-select
        v-model="value"
        :placeholder="placeholder"
        clearable
        style="position: relative;"
        :disabled="disabled"
        @on-clear="clear"
    >
        <i-option
            v-for="item of list"
            :key="item.value"
            :value="item.value"
            style="display: none"
        >
            {{ item.title }}
        </i-option>

        <i-tree
            ref="tree"
            :data="treeData"
            show-checkbox
            :check-strictly="false"
            @on-check-change="handleCheckChange"
        />
    </i-select>
</template>

<script>
import IOption from '@components/option';
import ISelect from '@components/select';
import ITree from '@components/tree';

const SPLIT = '::';

export default {
    value: 'MzMutualTreeSelect',

    components: {
        IOption,
        ISelect,
        ITree,
    },

    props: {
        // 选项数据
        options: {
            type: Array,
            required: true,
            validator: options =>
                options.every(opt =>
                    typeof opt.key !== 'undefined'
                    && typeof opt.value !== 'undefined'
                ),
        },
        // 占位符文本
        placeholder: {
            type: String,
            default: '请选择',
        },
        // 默认选中的值
        defaultValue: {
            type: Object,
            required: false,
            default: () => ({
                include: [],
                exclude: [],
            }),
        },
        // 包含组标题
        includeTitle: {
            type: String,
            default: '包含',
        },
        // 不包含组标题
        excludeTitle: {
            type: String,
            default: '不包含',
        },
        // 是否禁用
        disabled: {
            type: Boolean,
            default: false,
        },
    },

    data() {
        return {
            value: '',
            list: [{ title: '', value: '' }],
            selectedOptions: {
                include: [...(this.defaultValue?.include || [])],
                exclude: [...(this.defaultValue?.exclude || [])],
            },
            expandedNodes: new Set(),
            treeNodeStates: new Map(),
        };
    },

    computed: {
        treeData() {
            const groups = [
                {
                    type: 'include',
                    title: this.includeTitle,
                },
                {
                    type: 'exclude',
                    title: this.excludeTitle,
                },
            ];

            return groups.map(group => {
                const groupNode = {
                    value: group.type,
                    title: group.title,
                    expand: this.expandedNodes.has(group.type),
                    children: this.options.map(opt => {
                        const nodeKey = `${opt.key}${SPLIT}${group.type}`;
                        const currentState = this.treeNodeStates.get(nodeKey) || {};

                        return {
                            key: opt.key,
                            value: nodeKey,
                            title: opt.value,
                            disabled: group.type === 'include'
                                ? this.selectedOptions.exclude.includes(opt.key)
                                : this.selectedOptions.include.includes(opt.key),
                            checked: group.type === 'include'
                                ? this.selectedOptions.include.includes(opt.key)
                                : this.selectedOptions.exclude.includes(opt.key),
                            expand: currentState.expand,
                        };
                    }),
                };

                return groupNode;
            });
        },
    },

    watch: {
        defaultValue: {
            handler(newVal) {
                this.selectedOptions = {
                    include: [...(newVal.include || [])],
                    exclude: [...(newVal.exclude || [])],
                };

                this.updateDisplayValue();
            },
            deep: true,
        },
    },


    methods: {
        /**
         * 处理树节点选中状态变化
         * @param {Array} data - 当前选中的节点数据数组
         */
        handleCheckChange(data) {
            // 在更新选中状态前保存当前展开状态
            this.saveExpandedState();

            const selectedNodes = data.filter(item => !item.disabled);
            const include = [];
            const exclude = [];

            selectedNodes.forEach(node => {
                if (!node.value) return;
                const [key, type] = node.value.split(SPLIT);
                if (type === 'include') {
                    include.push(key);
                } else if (type === 'exclude') {
                    exclude.push(node.key);
                }
            });

            this.selectedOptions = { include, exclude };
            this.updateDisplayValue();
            this.$emit('change', this.selectedOptions);

            // 在下一个 tick 恢复展开状态
            this.$nextTick(() => {
                this.restoreExpandedState();
            });
        },

        /**
         * 保存树组件当前的展开状态
         * 遍历树节点，将展开的节点信息保存到 expandedNodes 和 treeNodeStates 中
         */
        saveExpandedState() {
            const tree = this.$refs.tree;
            if (!tree) return;

            this.expandedNodes.clear();
            this.treeNodeStates.clear();

            const saveNodeState = node => {
                if (!node) return;

                if (node.expand) {
                    if (node.value) {
                        this.expandedNodes.add(node.value);
                        this.treeNodeStates.set(node.value, {
                            expand: true,
                            checked: node.checked,
                        });
                    }
                }

                if (node.children && node.children.length) {
                    node.children.forEach(saveNodeState);
                }
            };

            tree.data.forEach(saveNodeState);
        },

        /**
         * 恢复树组件之前保存的展开状态
         * 遍历树节点，根据 expandedNodes 中的信息恢复节点的展开状态
         */
        restoreExpandedState() {
            const tree = this.$refs.tree;
            if (!tree) return;

            const expandNode = node => {
                if (!node) return;

                if (node.value && this.expandedNodes.has(node.value)) {
                    node.expand = true;
                }

                if (node.children && node.children.length) {
                    node.children.forEach(expandNode);
                }
            };

            tree.data.forEach(expandNode);
        },

        /**
         * 更新选择框显示的文本值
         * 根据当前选中的选项生成显示文本
         */
        updateDisplayValue() {
            const allSelected = [
                ...this.selectedOptions.include.map(key => ({
                    key,
                    type: 'include',
                    value: this.options.find(opt => opt.key === key)?.value,
                })),
                ...this.selectedOptions.exclude.map(key => ({
                    key,
                    type: 'exclude',
                    value: this.options.find(opt => opt.key === key)?.value,
                })),
            ].filter(item => item.value);

            if (allSelected.length === 0) {
                this.value = '';
                this.list = [{ title: '', value: '' }];
                return;
            }

            const displayText = allSelected
                .map(item => `${item.type === 'include' ? this.includeTitle : this.excludeTitle}:${item.value}`)
                .join(', ');

            this.value = displayText;
            this.list = [{
                value: displayText,
                title: displayText,
            }];
        },

        /**
         * 清空选择
         * 重置所有选中状态和显示值
         */
        clear() {
            this.selectedOptions = {
                include: [],
                exclude: [],
            };
            this.value = '';
            this.list = [{ title: '', value: '' }];
            this.$emit('change', this.selectedOptions);
        },
    },
};
</script>

