<template>
    <i-modal
        v-model="isShow"
        :title="titleConfig[hotType]"
        width="600"
        @on-visible-change="resetData"
    >
        <i-form
            ref="hotForm"
            :model="hotData"
            :rules="hotFormRules"
            :label-width="120"
        >
            <form-item
                v-show="!isAdd"
                prop="hotspot_id"
                label="热点ID："
            >
                <span>{{ hotData.hotspot_id }}</span>
            </form-item>
            <form-item
                prop="hotspot_name"
                label="热点名称："
            >
                <i-input
                    v-model.trim="hotData.hotspot_name"
                    class="input-text hotname-input"
                    maxlength="30"
                    show-word-limit
                    placeholder="请输入热点名称"
                    :disabled="isDetail || isOnline"
                />
            </form-item>
            <form-item
                prop="hotspot_introduction"
                label="热点简介："
            >
                <i-input
                    v-model.trim="hotData.hotspot_introduction"
                    class="input-text"
                    type="textarea"
                    :autosize="{minRows: 3,maxRows: 20}"
                    placeholder="请输入热点简介"
                    maxlength="100"
                    show-word-limit
                    :disabled="isDetail"
                />
            </form-item>
            <form-item
                prop="topic_selection"
                label="选题："
            >
                <i-input
                    v-model.trim="hotData.topic_selection"
                    type="textarea"
                    class="input-text"
                    placeholder="可添加多个选题，请使用“；”（中文分号）分割各个选题"
                    maxlength="450"
                    show-word-limit
                    :autosize="{minRows: 5,maxRows: 20}"
                    :disabled="isDetail"
                />
            </form-item>
            <form-item
                prop="sort"
                label="排序权重："
            >
                <input-number
                    v-model="hotData.sort"
                    class="input-text"
                    placeholder="请输入排序权重"
                    :min="1"
                    :max="999999"
                    precision="0"
                    :disabled="isDetail || isOnline"
                />
            </form-item>
            <form-item
                prop="hotspot_source"
                label="热点来源："
            >
                <i-input
                    v-model.trim="hotData.hotspot_source"
                    class="input-text"
                    type="textarea"
                    placeholder="请输入热点来源链接"
                    :disabled="isDetail || isOnline"
                />
            </form-item>
        </i-form>
        <div slot="footer">
            <i-button type="default" @click="toggleModal()">取消</i-button>
            <i-button type="primary" @click="onHotSubmit">确认</i-button>
        </div>
    </i-modal>
</template>
<script lang="tsx" setup>
import Api from '@/api/tools';
import axios from 'axios';
import { useToggle } from '@/hooks/useHelper';
import { ref, computed } from 'vue';
import InputNumber from '@components/input-number';
import Message from '@components/message';

import type { BaseReponse } from '@/interfaces/Base';
import type { SpotInfo } from './types';
import { StatusConf } from './data';

const titleConfig = {
    add: '新建',
    edit: '编辑',
    detail: '详情',
};

const emits = defineEmits(['change']);


const hotData = ref({
    hotspot_id: null,
    hotspot_name: '',
    hotspot_source: '',
    hotspot_introduction: '',
    topic_selection: '',
    status: 0,
    sort: null,
});

const hotFormRules = {
    hotspot_name: {
        required: true,
        message: '请输入热点名称',
    },
    hotspot_source: {
        required: true,
        validator: (rule, value, callback) => {
            if (!value) {
                callback(new Error('请输入热点来源链接'));
            } else if (!validateURL(value)) {
                callback(new Error('请输入正确的来源链接'));
            }
            callback();
        },
    },
    hotspot_introduction: {
        required: true,
        message: '请输入热点简介',
    },
    topic_selection: {
        required: true,
        message: '请输入选题',
    },
    sort: {
        required: true,
        message: '请输入排序权重',
    },
};

const hotType = ref('add');
const hotForm = ref(null);

const [isShow, toggleModal] = useToggle(false);

const isAdd = computed(() => hotType.value === 'add');
const isDetail = computed(() => hotType.value === 'detail');
const isOnline = computed(() => hotData.value.status === StatusConf.OnLine);

const open = ({ type, id }) => {
    toggleModal();
    hotType.value = type;
    hotData.value.hotspot_id = id;
    if (type === 'edit' || type === 'detail') {
        getDetail();
    };
};

const resetData = (tag:Boolean) => {
    if (!tag) {
        hotData.value = {
            hotspot_id: null,
            hotspot_name: '',
            hotspot_source: '',
            hotspot_introduction: '',
            topic_selection: '',
            status: 0,
            sort: null,
        };
        hotForm.value?.resetFields();
        hotData.value.hotspot_id = null;
    }
};

const getDetail = async () => {
    const { errno, data } = await axios.get<void, BaseReponse<SpotInfo>>(Api.traffichotspotdetail, {
        params: {
            hotspot_id: hotData.value.hotspot_id,
        },
    });

        if (errno === 0) {
            hotData.value = data;
        }
};

const onHotSubmit = () => {
    if (hotType.value === 'detail') {
        toggleModal();
        return;
    }
    hotForm.value?.validate(async valid => {
        if (valid) {
            const requestApi = hotType.value === 'add' ? Api.traffichotspotcreate : Api.traffichotspotedit;

            const { errno } = await axios.post <void, BaseReponse<{}>>(requestApi, {
                ...hotData.value,
            });

            if (errno === 0) {
                Message.success('保存成功');
                emits('change');
                toggleModal();
            }
        };
    });
};

function validateURL(url) {
    // 验证自定义协议和标准URL
    const pattern = /^[a-zA-Z][a-zA-Z\d+\-.]*:[^\s]*$/;

    try {
        // URL构造函数检查标准 URL
        new URL(url);
        return true;
    } catch (e) {
        return pattern.test(url);
    }
};



defineExpose({
    open,
});
</script>
<style lang="less" scoped>
.input-text {
    width: 400px;
}

.hotname-input {
    /deep/ .ivu-input {
        padding-right: 43px;
    }
}
</style>