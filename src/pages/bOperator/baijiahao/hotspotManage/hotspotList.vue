<template>
    <div>
        <mz-title title="热点管理" />
        <base-search
            class="header"
            :search-list="searchList"
            :search-fields="searchFields"
            @onSubmit="onSearch"
        >
            <template #export>
                <i-button
                    class="c-gap-left-small"
                    type="primary"
                    icon="md-add"
                    shape="circle"
                    @click="openModal({type: 'add'})"
                >新增</i-button>
                <i-button
                    class="c-gap-left-small"
                    type="primary"
                    shape="circle"
                    @click="resetSearch"
                >重置</i-button>
            </template>
        </base-search>
        <div class="c-gap-top">
            <base-table
                class="addTable"
                :loading="loading"
                :columns="tableColumns"
                :data-source="dataSource"
                :pagination="pagination"
            />
        </div>
        <edit-modal
            ref="editModalRef"
            @change="onSearch"
        />
    </div>
</template>

<script lang="tsx" setup>
import BaseSearch from '@pages/add/components/BaseSearch/index.vue';
import MzTitle from '@components/mz-title';
import Message from '@components/message';
import editModal from './editModal.vue';

import axios from 'axios';
import Api from '@/api/tools';
import type { BaseReponse, List } from '@/interfance/Base';
import type { SpotInfo, ModalHandleDataType } from './types';
import { StatusConf } from './data';
import { ref, reactive } from 'vue';
import { useRouter } from '@/hooks/router';

const searchList = ref({
    hotspot_id: '',
    hotspot_name: '',
});

const pagination = reactive({
    tablePagination: {
        pageSize: 20,
        pageIndex: 1,
        total: 0,
    },
    onChange: data => {
        pagination.tablePagination.pageIndex = data;
        getList();
    },
});

const editModalRef = ref();


// 头部筛选
const searchFields = [
    {
        key: 'hotspot_id',
        type: 'input',
        placeholder: '热点id',
    },
    {
        key: 'hotspot_name',
        type: 'input',
        placeholder: '热点名称',
    },
];


// 表格
const tableColumns = [
    {
        key: 'hotspot_id',
        title: '热点id',
        align: 'center',
        minWidth: 80,
    },
    {
        key: 'hotspot_name',
        title: '热点名称',
        align: 'center',
        minWidth: 120,
    },
    {
        key: 'topic_selection',
        title: '选题',
        align: 'center',
        minWidth: 350,
    },
    {
        key: 'sort',
        title: '排序权重',
        align: 'center',
        minWidth: 80,
    },
    {
        key: 'status',
        title: '状态',
        align: 'center',
        minWidth: 80,
        render: (h, { row: { status } }) => (
            <span>{status === StatusConf.OnLine ? '已上线' : '已下线'}</span>
        ),
    },
    {
        key: 'create_user',
        title: '创建人',
        align: 'center',
        minWidth: 100,
    },
    {
        key: 'create_time',
        title: '创建时间',
        align: 'center',
        minWidth: 140,
    },
    {
        title: '操作',
        align: 'center',
        minWidth: 140,
        fixed: 'right',
        render: (h, { row }) => (
            <div>
                <a
                    class="c-gap-right-small"
                    onClick={() => openModal({ type: 'detail', id: row.hotspot_id })}
                >查看</a>
                <a
                    class="c-gap-right-small"
                    onClick={() => openModal({ type: 'edit', id: row.hotspot_id })}
                >编辑</a>
                <a
                    onClick={() => onStatusChange(row)}
                >{row.status === StatusConf.OnLine ? '下线' : '上线'}</a>
            </div>
        ),
    },
];


const loading = ref(false);
const dataSource = ref([]);

const resetSearch = () => {
    searchList.value = {
        hotspot_id: '',
        hotspot_name: '',
    };
    pagination.tablePagination.pageIndex = 1;
    getList();
};

const onSearch = () => {
    if (searchList.value.hotspot_id && isNaN(Number(searchList.value.hotspot_id))) {
        Message.error('请输入正确的热点id');
        return;
    }
    pagination.tablePagination.pageIndex = 1;
    getList();
};

async function getList() {
    loading.value = true;
    const { tablePagination: { pageSize, pageIndex } } = pagination;
    const { errno, data } = await axios.get<void, BaseReponse<List<SpotInfo>>>(Api.traffichotspotlist, {
        params: {
            hotspot_id: +searchList.value.hotspot_id || null,
            hotspot_name: searchList.value.hotspot_name,
            page_num: pageIndex,
            page_size: pageSize,
        },
    });

    if (errno === 0) {
        dataSource.value = data.list;
        loading.value = false;
        pagination.tablePagination.total = data.total;
    }
};

getList();

const openModal = (data: ModalHandleDataType) => {
    editModalRef.value?.open(data);
};


const onStatusChange = async data => {
    const { OnLine, OffLine } = StatusConf;
    const { errno } = await axios.post<void, BaseReponse<{}>>(Api.traffichotspotoperate, {
        hotspot_id: data.hotspot_id,
        status: data.status === OnLine ? OffLine : OnLine,
    });
    if (errno === 0) {
        Message.success('更新成功');
        getList();
    };
};


</script>
<style scoped lang='less'>
.input-text {
    width: 300px;
}

.limit-input /deep/ .ivu-input {
    padding-right: 43px;
}
</style>
