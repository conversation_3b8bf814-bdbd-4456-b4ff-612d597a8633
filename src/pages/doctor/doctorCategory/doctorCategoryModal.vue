<template>
    <i-modal
        :value="value"
        :title="title"
        footer-hide
        width="600"
        @input="closeModal"
        @on-visible-change="onReset"
    >
        <!-- 模式切换按钮 -->
        <div class="mode-switch-container">
            <div class="mode-button-group">
                <i-button
                    class="mode-button mode-button-left"
                    :type="mode === 'basic' ? 'primary' : 'default'"
                    @click="switchMode('basic')"
                >
                    基础模式
                </i-button>
                <i-button
                    class="mode-button mode-button-right"
                    :type="mode === 'advanced' ? 'primary' : 'default'"
                    @click="switchMode('advanced')"
                >
                    高级模式
                </i-button>
            </div>
        </div>

        <!-- 基础模式 -->
        <doctor-category
            v-if="mode === 'basic'"
            :id="id"
            ref="categoryForm"
            :data="data"
            :max-height="600"
            :disabled="disabled"
            @submit="submit"
        />

        <!-- 高级模式 -->
        <div v-else-if="mode === 'advanced'" class="advanced-mode-container">
            <div class="advanced-header">
                <h3>自定义条件</h3>
                <i-button
                    type="primary"
                    size="small"
                    @click="loadFromBasic"
                >
                    载入字典
                </i-button>
            </div>
            <i-input
                v-model="advancedCondition"
                type="textarea"
                :rows="20"
                placeholder="请输入自定义条件..."
                class="advanced-textarea"
            />
            <div class="advanced-footer">
                <div class="predicted-count">
                    预计人数（不含自定义uid）：{{ predictedCount }}
                    <a @click="refreshCount">刷新</a>
                </div>
                <i-button
                    v-if="!disabled"
                    type="primary"
                    @click="submitAdvanced"
                >
                    保存
                </i-button>
                <i-button
                    v-else
                    type="primary"
                    disabled
                >
                    仅可查看，不可修改
                </i-button>
            </div>
        </div>
    </i-modal>
</template>

<script lang="tsx" setup>
import doctorCategory from './doctorCategory.vue';
import { DoctorCategoryData } from './type';
import { ref, watch } from 'vue';

const props = withDefaults(defineProps<{
    value: boolean;
    title: string;
    id?: string;
    disabled?: boolean,
    data: DoctorCategoryData
}>(), {
    value: false,
    title: '编辑条件',
    id: '',
    disabled: false,
});

const categoryForm = ref();
const mode = ref<'basic' | 'advanced'>('basic');
const advancedCondition = ref('');
const predictedCount = ref(0);

const emit = defineEmits(['input', 'submit']);

const closeModal = () => {
    emit('input', false);
};

const submit = (params: any) => {
    emit('submit', params);
};

const onReset = (status: boolean) => {
    if (!status) {
        categoryForm.value?.reset();
        mode.value = 'basic';
        advancedCondition.value = '';
        predictedCount.value = 0;
    }
};

const switchMode = (newMode: 'basic' | 'advanced') => {
    mode.value = newMode;
};

const loadFromBasic = () => {
    // 这里可以从基础模式加载条件到高级模式
    // 暂时显示一个示例
    advancedCondition.value = `{
    "user_range": "1",
    "user_regist_type": "1",
    "user_type": ["其他"],
    "auth_type": ["信誉认证通过"],
    "forbidden_end": ["未封禁"],
    "acc_delete_status": ["未注销"],
    "company_grade": ["未知"],
    "city_range": [],
    "hospitals": [],
    "clinical_title": [],
    "std_practice_depts": [],
    "cid2": [],
    "rights_str": {
        "include": [],
        "exclude": []
    }
}`;
};

const refreshCount = () => {
    // 刷新预计人数的逻辑
    predictedCount.value = Math.floor(Math.random() * 10000);
};

const submitAdvanced = () => {
    try {
        const parsedCondition = JSON.parse(advancedCondition.value);
        emit('submit', parsedCondition);
    } catch (error) {
        console.error('JSON格式错误:', error);
        // 这里可以添加错误提示
    }
};

// 监听数据变化，初始化高级模式的内容
watch(() => props.data, newData => {
    if (newData && Object.keys(newData).length > 0) {
        advancedCondition.value = JSON.stringify(newData, null, 2);
    }
}, { immediate: true });
</script>

<style lang="less" scoped>
.mode-switch-container {
    margin-bottom: 16px;
    text-align: center;
    border-bottom: 1px solid #e8eaec;
    padding-bottom: 16px;

    .mode-button-group {
        display: inline-block;

        .mode-button {
            margin: 0;

            &.mode-button-left {
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
                border-right: none;
            }

            &.mode-button-right {
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
                margin-left: -1px;
            }
        }
    }
}

.advanced-mode-container {
    .advanced-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
        }
    }

    .advanced-textarea {
        margin-bottom: 12px;

        :deep(.ivu-input) {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
    }

    .advanced-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .predicted-count {
            font-size: 14px;
            color: #666;

            a {
                color: #2d8cf0;
                cursor: pointer;
                text-decoration: none;
                margin-left: 8px;

                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }
}
</style>
