<template>
    <div>
        <i-form
            ref="doctorCategoryForm"
            class="doctor-category-form"
            :rules="formRules"
            :model="formData"
            :style="modalStyle"
        >
            <form-item label="全部筛选: " prop="user_range">
                <i-select
                    v-model="formData.user_range"
                    style="width: 200px;"
                    placeholder="请选择筛选用户"
                    clearable
                >
                    <i-option
                        v-for="item in nameListMap.filterRange"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </i-select>
                <i-button
                    type="primary"
                    class="close-btn"
                    @click="formData.user_range = undefined;"
                >清空</i-button>
            </form-item>
            <!-- 选择筛选用户, 出认证信息 -->
            <template v-if="showDoctorConfig.showSettled">
                <h3>认证信息</h3>
                <form-item
                    label="用户入驻: "
                    prop="user_regist_type"
                >
                    <radio-group
                        v-model="formData.user_regist_type"
                    >
                        <i-radio
                            v-for="item in nameListMap.settledType"
                            :key="item.value"
                            :label="item.value"
                        >
                            {{ item.label }}
                        </i-radio>
                    </radio-group>
                </form-item>
                <form-item
                    label="是否注册标品doc_id: "
                    prop="is_exist_doc_id"
                >
                    <radio-group
                        v-model="formData.is_exist_doc_id"
                    >
                        <i-radio
                            v-for="(val, key) of nameListMap.yesOrNo"
                            :key="key"
                            :label="key"
                            @click.native="unCheck(key, 'is_exist_doc_id')"
                        >
                            {{ val }}
                        </i-radio>
                    </radio-group>
                </form-item>
                <!-- 入驻 分层 -->
                <template v-if="showDoctorConfig.showRegister">
                    <form-item
                        label="职业: "
                        class="form-item-checkbox-over"
                        prop="user_type"
                    >
                        <i-check-box-group
                            v-model="formData.user_type"
                        >
                            <i-check-box
                                v-for="(value, key) of configListMap.field_items"
                                :key="key"
                                :label="key"
                            >
                                {{ value }}
                            </i-check-box>
                        </i-check-box-group>
                    </form-item>
                    <form-item label="医生认证状态：" prop="auth_type">
                        <i-check-box-group
                            v-model="formData.auth_type"
                        >
                            <i-check-box
                                v-for="item in nameListMap.scopeCertification"
                                :key="item.value"
                                :label="item.value"
                            >
                                {{ item.label }}
                            </i-check-box>
                        </i-check-box-group>
                    </form-item>
                    <form-item label="封禁状态：" prop="forbidden_end">
                        <i-check-box-group
                            v-model="formData.forbidden_end"
                        >
                            <i-check-box
                                v-for="(value, key) in configListMap.forbidden_status_options"
                                :key="key"
                                :label="key"
                            >
                                {{ value }}
                            </i-check-box>
                        </i-check-box-group>
                    </form-item>
                    <form-item label="注销状态：" prop="acc_delete_status">
                        <i-check-box-group
                            v-model="formData.acc_delete_status"
                        >
                            <i-check-box
                                v-for="item in configListMap.doctor_acc_info_options"
                                :key="item.key"
                                :label="item.key"
                            >
                                {{ item.value }}
                            </i-check-box>
                        </i-check-box-group>
                    </form-item>

                    <h3>个人信息</h3>
                    <form-item
                        class="form-item-checkbox-over"
                        label="医院等级："
                        prop="company_grade"
                    >
                        <i-check-box-group
                            v-model="formData.company_grade"
                        >
                            <i-check-box
                                v-for="(value, key) in configListMap.hospital_level_items"
                                :key="key"
                                :label="key"
                            >
                                {{ value }}
                            </i-check-box>
                        </i-check-box-group>
                    </form-item>
                    <form-item label="医院城市：" prop="city_range">
                        <i-tree-select-city
                            ref="cityTree"
                            :default-city="formData.city_range"
                            @change="cityChange"
                            @clear="handleClearCity"
                        />
                    </form-item>
                    <form-item

                        label="医院性质："
                        prop="company_property"
                    >
                        <i-check-box-group
                            v-model="formData.company_property"
                        >
                            <i-check-box
                                v-for="(value, key) in configListMap.company_property_options"
                                :key="key"
                                :label="key"
                            >
                                {{ value }}
                            </i-check-box>
                        </i-check-box-group>
                    </form-item>
                    <form-item
                        class="form-item-checkbox-over"
                        label="医院："
                        prop="hospitals"
                    >
                        <i-select
                            v-model="hosp"
                            filterable
                            multiple
                            :remote-method="remoteHospitalMethod"
                            style="width: 250px"
                            :label-in-value="true"
                        >
                            <i-option
                                v-for="item in hospitalOptionsList"
                                :key="item.area_id"
                                :value="item.area_id"
                                :label="item.area_name"
                            />
                        </i-select>
                    </form-item>
                    <form-item
                        label="复旦医院综合等级："
                        prop="fudan_grade"
                    >
                        <i-check-box-group
                            v-model="formData.fudan_grade"
                        >
                            <i-check-box
                                v-for="(value, key) in new_fudan_grade_config"
                                :key="key"
                                :label="value"
                            >
                                {{ value }}
                            </i-check-box>
                        </i-check-box-group>
                    </form-item>
                    <form-item
                        label="复旦全国专科排名："
                        prop="fudan"
                    >
                        <div flex>
                            <i-select
                                v-model="formData.fudan_all_department_rank_compare"
                                style="width: 100px"
                                placeholder="运算逻辑"
                            >
                                <i-option
                                    v-for="(val, key) in nameListMap.compareMap"
                                    :key="key"
                                    :value="val"
                                    :label="val"
                                />
                            </i-select>
                            <i-input v-model="formData.fudan_all_department_rank" style="width: 100px;" />
                            <span class="c-color-gray c-gap-left">专科排名最大为10</span>
                        </div>
                    </form-item>
                    <!-- <form-item

                        label="专科医院排名："
                        prop="specialty_hospital"
                    >
                        <div flex>
                            <i-input v-model="formData.specialty_hospital" style="width: 100px;" />
                            <i-select
                                v-model="formData.specialty_hospital_compare"
                                style="width: 100px"
                                placeholder="运算逻辑"
                            >
                                <i-option
                                    v-for="(val, key) in nameListMap.compareMap"
                                    :key="key"
                                    :value="key"
                                    :label="val"
                                />
                            </i-select>
                            <i-input v-model="formData.specialty_hospital_rank" style="width: 100px;" />
                        </div>
                    </form-item> -->
                    <form-item
                        v-if="showDoctorConfig.showTitle"
                        label="职称："
                        class="form-item-checkbox-over"
                        prop="clinical_title"
                    >
                        <i-check-box-group
                            v-model="formData.clinical_title"
                        >
                            <i-check-box
                                v-for="(title, key) in configListMap.clinical_title_items"
                                :key="key"
                                :label="key"
                            >
                                {{ title }}
                            </i-check-box>
                        </i-check-box-group>
                    </form-item>
                    <form-item label="标准执业范围：" prop="std_practice_depts">
                        <i-tree-select-city
                            ref="deptsTree"
                            type="practicDepartment"
                            placeholder="标准执业范围"
                            :default-city="defaultDepts"
                            @change="practiceChange"
                            @clear="handleClearPractice"
                        />
                    </form-item>
                    <form-item label="医院科室：" prop="cidTitle">
                        <i-tree-select-cids
                            ref="cidTree"
                            :echo-title="formData.cidTitle"
                            style="width: 200px;"
                            @change="cidChange"
                            @clearCids="handleClearCids"
                        />
                    </form-item>

                    <!-- <h3>权益信息</h3>
                    <form-item
                        class="form-item-checkbox-over"
                        label="权益标签："
                    >
                        <i-check-box-group
                            v-model="formData.rights_bit"
                        >
                            <i-check-box
                                v-for="item in configListMap.rigit_items"
                                :key="item.key"
                                :label="item.key"
                            >
                                {{ item.value }}
                            </i-check-box>
                        </i-check-box-group>
                    </form-item> -->

                    <h3>权益信息</h3>
                    <mz-mutual-tree-select
                        ref="mutualTreeSelectRef"
                        style="width: 300px; margin-bottom: 20px;"
                        placeholder="功能权益"
                        include-title="开通"
                        exclude-title="未开通"
                        :options="configListMap.rights_items || []"
                        :default-value="defaultRightsNew"
                        @change="handleFunctionRightsChange"
                    />

                    <h3>运营标记</h3>
                    <form-item label="是否精选医生：" prop="is_concentrate">
                        <radio-group
                            v-model="formData.is_concentrate"
                        >
                            <i-radio
                                v-for="(val, key) of nameListMap.yesOrNo"
                                :key="key"
                                :label="key"
                                @click.native="unCheck(key, 'is_concentrate')"
                            >
                                {{ val }}
                            </i-radio>
                        </radio-group>
                    </form-item>
                    <form-item label="是否自雇医生：" prop="is_self">
                        <radio-group
                            v-model="formData.is_self"
                        >
                            <i-radio
                                v-for="(val, key) of nameListMap.yesOrNo"
                                :key="key"
                                :label="key"
                                @click.native="unCheck(key, 'is_self')"
                            >
                                {{ val }}
                            </i-radio>
                        </radio-group>
                    </form-item>
                    <form-item label="是否医美医生：" prop="is_medical_cosmetology">
                        <radio-group
                            v-model="formData.is_medical_cosmetology"
                        >
                            <i-radio
                                v-for="(val, key) of nameListMap.yesOrNo"
                                :key="key"
                                :label="key"
                                @click.native="unCheck(key, 'is_medical_cosmetology')"
                            >
                                {{ val }}
                            </i-radio>
                        </radio-group>
                    </form-item>
                    <h3>用户行为</h3>
                    <form-item

                        label="入驻时长："
                        prop="new_regist_days"
                    >
                        <i-select
                            v-model="formData.new_regist_days_compare"
                            class="c-gap-right c-inline-block regist-box"
                            style="width: 90px;"
                            placeholder="运算逻辑"
                            clearable
                        >
                            <i-option
                                v-for="(val, key) in nameListMap.timeCompareMap"
                                :key="key"
                                :value="val"
                                :label="val"
                            />
                        </i-select>
                        <div class="c-inline-block">
                            <i-input
                                v-model="formData.new_regist_days"
                                style="width: 90px;"
                                type="number"
                                :min="1"
                            >
                                <span slot="append" style="width: 70px">天</span>
                            </i-input>
                        </div>
                    </form-item>
                    <form-item

                        label="不活跃时长："
                        prop="no_active_day"
                    >
                        <i-select
                            v-model="formData.no_active_day_compare"
                            class="c-gap-right c-inline-block regist-box"
                            style="width: 90px;"
                            placeholder="运算逻辑"
                            clearable
                        >
                            <i-option
                                v-for="(val, key) in nameListMap.timeCompareMap"
                                :key="key"
                                :value="val"
                                :label="val"
                            />
                        </i-select>
                        <div class="c-inline-block">
                            <i-input
                                v-model="formData.no_active_day"
                                style="width: 90px;"
                                :min="1"
                                type="number"
                            >
                                <span slot="append" style="width: 70px">天</span>
                            </i-input>
                        </div>
                    </form-item>
                    <form-item

                        label="问诊最后接诊时间："
                        prop="last_consult_claim_days"
                    >
                        <i-select
                            v-model="formData.last_consult_claim_days_compare"
                            class="c-gap-right c-inline-block regist-box"
                            style="width: 90px;"
                            placeholder="运算逻辑"
                            clearable
                        >
                            <i-option
                                v-for="(val, key) in nameListMap.timeCompareMap"
                                :key="key"
                                :value="val"
                                :label="val"
                            />
                        </i-select>
                        <div class="c-inline-block">
                            <i-input
                                v-model="formData.last_consult_claim_days"
                                style="width: 90px;"
                                :min="1"
                                type="number"
                            >
                                <span slot="append" style="width: 70px">天</span>
                            </i-input>
                        </div>
                    </form-item>
                    <form-item

                        label="加号最后订单时间："
                        prop="last_jh_order_days"
                    >
                        <i-select
                            v-model="formData.last_jh_order_days_compare"
                            class="c-gap-right c-inline-block regist-box"
                            style="width: 90px;"
                            placeholder="运算逻辑"
                            clearable
                        >
                            <i-option
                                v-for="(val, key) in nameListMap.timeCompareMap"
                                :key="key"
                                :value="val"
                                :label="val"
                            />
                        </i-select>
                        <div class="c-inline-block">
                            <i-input
                                v-model="formData.last_jh_order_days"
                                style="width: 90px;"
                                :min="1"
                                type="number"
                            >
                                <span slot="append" style="width: 70px">天</span>
                            </i-input>
                        </div>
                    </form-item>
                    <form-item

                        label="报到最后审核时间："
                        prop="last_patient_report_audit_days"
                    >
                        <i-select
                            v-model="formData.last_patient_report_audit_days_compare"
                            class="c-gap-right c-inline-block regist-box"
                            style="width: 90px;"
                            placeholder="运算逻辑"
                            clearable
                        >
                            <i-option
                                v-for="(val, key) in nameListMap.timeCompareMap"
                                :key="key"
                                :value="val"
                                :label="val"
                            />
                        </i-select>
                        <div class="c-inline-block">
                            <i-input
                                v-model="formData.last_patient_report_audit_days"
                                style="width: 90px;"
                                :min="1"
                                type="number"
                            >
                                <span slot="append" style="width: 70px">天</span>
                            </i-input>
                        </div>
                    </form-item>
                    <form-item

                        label="百家号（全类型）最后发文时间："
                        prop="last_bjh_publish_days"
                    >
                        <i-select
                            v-model="formData.last_bjh_publish_days_compare"
                            class="c-gap-right c-inline-block regist-box"
                            style="width: 90px;"
                            placeholder="运算逻辑"
                            clearable
                        >
                            <i-option
                                v-for="(val, key) in nameListMap.timeCompareMap"
                                :key="key"
                                :value="val"
                                :label="val"
                            />
                        </i-select>
                        <div class="c-inline-block">
                            <i-input
                                v-model="formData.last_bjh_publish_days"
                                style="width: 90px;"
                                :min="1"
                                type="number"
                            >
                                <span slot="append" style="width: 70px">天</span>
                            </i-input>
                        </div>
                    </form-item>
                    <form-item

                        label="百家号图文最后发文时间："
                        prop="last_bjh_img_text_publish_days"
                    >
                        <i-select
                            v-model="formData.last_bjh_img_text_publish_days_compare"
                            class="c-gap-right c-inline-block regist-box"
                            style="width: 90px;"
                            placeholder="运算逻辑"
                            clearable
                        >
                            <i-option
                                v-for="(val, key) in nameListMap.timeCompareMap"
                                :key="key"
                                :value="val"
                                :label="val"
                            />
                        </i-select>
                        <div class="c-inline-block">
                            <i-input
                                v-model="formData.last_bjh_img_text_publish_days"
                                style="width: 90px;"
                                :min="1"
                                type="number"
                            >
                                <span slot="append" style="width: 70px">天</span>
                            </i-input>
                        </div>
                    </form-item>
                    <form-item

                        label="百家号短视频文最后发文时间："
                        prop="last_bjh_short_video_publish_days"
                    >
                        <i-select
                            v-model="formData.last_bjh_short_video_publish_days_compare"
                            class="c-gap-right c-inline-block regist-box"
                            style="width: 90px;"
                            placeholder="运算逻辑"
                            clearable
                        >
                            <i-option
                                v-for="(val, key) in nameListMap.timeCompareMap"
                                :key="key"
                                :value="val"
                                :label="val"
                            />
                        </i-select>
                        <div class="c-inline-block">
                            <i-input
                                v-model="formData.last_bjh_short_video_publish_days"
                                style="width: 90px;"
                                :min="1"
                                type="number"
                            >
                                <span slot="append" style="width: 70px">天</span>
                            </i-input>
                        </div>
                    </form-item>
                    <form-item

                        label="百家号小视频文最后发文时间："
                        prop="last_bjh_small_video_publish_days"
                    >
                        <i-select
                            v-model="formData.last_bjh_small_video_publish_days_compare"
                            class="c-gap-right c-inline-block regist-box"
                            style="width: 90px;"
                            placeholder="运算逻辑"
                            clearable
                        >
                            <i-option
                                v-for="(val, key) in nameListMap.timeCompareMap"
                                :key="key"
                                :value="val"
                                :label="val"
                            />
                        </i-select>
                        <div class="c-inline-block">
                            <i-input
                                v-model="formData.last_bjh_small_video_publish_days"
                                style="width: 90px;"
                                :min="1"
                                type="number"
                            >
                                <span slot="append" style="width: 70px">天</span>
                            </i-input>
                        </div>
                    </form-item>
                    <form-item

                        label="百家号动态发文最后发文时间："
                        prop="last_bjh_dynamic_publish_days"
                    >
                        <i-select
                            v-model="formData.last_bjh_dynamic_publish_days_compare"
                            class="c-gap-right c-inline-block regist-box"
                            style="width: 90px;"
                            placeholder="运算逻辑"
                            clearable
                        >
                            <i-option
                                v-for="(val, key) in nameListMap.timeCompareMap"
                                :key="key"
                                :value="val"
                                :label="val"
                            />
                        </i-select>
                        <div class="c-inline-block">
                            <i-input
                                v-model="formData.last_bjh_dynamic_publish_days"
                                style="width: 90px;"
                                :min="1"
                                type="number"
                            >
                                <span slot="append" style="width: 70px">天</span>
                            </i-input>
                        </div>
                    </form-item>
                    <h3>自定义uid（请到该名单的UID列表中查询和管理）</h3>
                    <form-item
                        class="form-item-checkbox-over"
                        label="条件和UID的运算关系："
                        prop="div_uid"
                    >
                        <div>
                            <div>
                                <span>运算关系</span>
                                <radio-group
                                    v-model="formData.diy_operation"
                                >
                                    <i-radio
                                        v-for="(val, key) of nameListMap.operationOptions"
                                        :key="key"
                                        :label="key"
                                    >
                                        {{ val }}
                                    </i-radio>
                                </radio-group>
                            </div>
                        </div>
                    </form-item>
                </template>
                <!-- 非入驻分层 -->
                <template v-if="showDoctorConfig.showUnregister">
                    <form-item label="是否登录：" prop="is_login">
                        <radio-group
                            v-model="formData.is_login"
                        >
                            <i-radio
                                v-for="item of configListMap.user_login_options"
                                :key="item.key"
                                :label="item.key"
                            >
                                {{ item.value }}
                            </i-radio>
                        </radio-group>
                    </form-item>
                    <template v-if="formData.is_login === '1'">
                        <form-item label="实名状态：" prop="real_name_status">
                            <radio-group
                                v-model="formData.real_name_status"
                            >
                                <i-radio
                                    v-for="item of configListMap.real_name_status_options"
                                    :key="item.key"
                                    :label="item.key"
                                >
                                    {{ item.value }}
                                </i-radio>
                            </radio-group>
                        </form-item>
                        <form-item label="封禁状态：" prop="forbidden_end">
                            <i-check-box-group
                                v-model="formData.forbidden_end"
                            >
                                <i-check-box
                                    v-for="(value, key) in configListMap.forbidden_status_options"
                                    :key="key"
                                    :label="key"
                                >
                                    {{ value }}
                                </i-check-box>
                            </i-check-box-group>
                        </form-item>
                        <form-item label="注销状态：" prop="acc_delete_status">
                            <i-check-box-group
                                v-model="formData.acc_delete_status"
                            >
                                <i-check-box
                                    v-for="item in configListMap.doctor_acc_info_options"
                                    :key="item.key"
                                    :label="item.key"
                                >
                                    {{ item.value }}
                                </i-check-box>
                            </i-check-box-group>
                        </form-item>
                    </template>

                </template>
            </template>
        </i-form>
        <div flex="main:justify">
            <div style="line-height: 32px">
                <span>预计人数（不含自定义uid）：{{ pre_total }}</span>
                <a @click="getTotal">刷新</a>
            </div>
            <i-button
                v-if="!disabled"
                type="primary"
                @click="submit"
            >保存</i-button>
            <i-button
                v-else
                type="primary"
                disabled="disabled"
            >仅可查看，不可修改</i-button>
        </div>
    </div>

</template>

<script lang="tsx" setup>
import Api from '@/api/doctor';
import MzMutualTreeSelect from '@BusComponents/mz-mutual-tree-select';
import axios from 'axios';
import { ref, computed, watch, nextTick } from 'vue';
import nameListMap from '../nameListMap.js';
import { fudan_grade_config } from '../listData';
import { BaseReponse } from '@/interfance/Base';
import { DoctorCategoryData, SelectItems, Hospital } from './type';
import { normalizedDoctorCategoryParams } from './util';
import { getPreTotal } from './api';

const props = withDefaults(defineProps<{
    id?: string,
    maxHeight?: number,
    disabled?: boolean,
    data: DoctorCategoryData
}>(),
{
    id: '',
    disabled: false,
    });
const modalStyle = computed(() => {
    return props.maxHeight ? {
        maxHeight: props.maxHeight + 'px',
        overflow: 'auto',
    } : {};
});
// 医院名称
const hosp = ref<string[]>([]);
const hospitalOptionsList = ref<Hospital[]>([]);

const defaultDepts = ref([]);

const pre_total = ref(0);
const mutualTreeSelectRef = ref(null);
const formData = ref<DoctorCategoryData>({});
watch(() => props.data, data => {
    formData.value = data;
    defaultDepts.value = data.std_practice_depts;
    hospitalOptionsList.value = data?.hospitals?.map(item => {
        return {
            ...item,
            area_id: item.company_area_id,
        };
    });
    nextTick(() => {
        hosp.value = data?.hospitals?.map(item => {
            return item.company_area_id;
        });
        if (mutualTreeSelectRef.value && mutualTreeSelectRef.value.updateDisplayValue) {
            mutualTreeSelectRef.value.updateDisplayValue();
        }
    });
    getTotal();
});
const new_fudan_grade_config = fudan_grade_config.filter(item => item !== '无');

const getTotal = async () => {
    const hospitals = [];
    hosp.value.forEach(el => {
        hospitalOptionsList.value.forEach(item => {
            if (el === item.area_id) {
                hospitals.push(
                    {
                        'company_area_id': parseInt(item.area_id, 10),
                        'area_name': item.area_name,
                    }
                );
            }
        });
    });
    formData.value.hospitals = hospitals;
    const { data, errno } = await getPreTotal(normalizedDoctorCategoryParams(formData.value, props.id));
    if (errno === 0) {
        pre_total.value = data.total;
    }
};

const remoteHospitalMethod = async hospital_name => {
        const { errno, data } = await axios.get<void, BaseReponse<{list: Hospital[]}>>(Api.getHospList, {
            params: {
                hospital_name,
            },
        });

    if (errno === 0) {
        hospitalOptionsList.value = data.list;
    }
};

const formRules = ref({
    new_regist_days: [
        {
            required: false,
            trigger: 'change',
            validator: (rule, value, callback) => {
                if (!formData.value.new_regist_days_compare !== !value) {
                    callback(new Error('入驻时长必须填写完整！'));
                }
                if (value && +value < 1) {
                    callback(new Error('填写的天数需要大于等于1'));
                }
                callback();
            },

        },
    ],
    no_active_day: [
        {
            required: false,
            trigger: 'change',
            validator: (rule, value, callback) => {
                if (!formData.value.no_active_day_compare !== !value) {
                    callback(new Error('不活跃时长必须填写完整！'));
                }
                if (value && +value < 1) {
                    callback(new Error('填写的天数需要大于等于1'));
                }
                callback();
            },
        },
    ],
    last_consult_claim_days: [
        {
            required: false,
            trigger: 'change',
            validator: (rule, value, callback) => {
                if (!formData.value.last_consult_claim_days_compare !== !value) {
                    callback(new Error('请完整填写！'));
                }
                if (value && +value < 1) {
                    callback(new Error('填写的天数需要大于等于1'));
                }
                callback();
            },
        },
    ],
    last_jh_order_days: [
        {
            required: false,
            trigger: 'change',
            validator: (rule, value, callback) => {
                if (!formData.value.last_jh_order_days_compare !== !value) {
                    callback(new Error('请完整填写！'));
                }
                if (value && +value < 1) {
                    callback(new Error('填写的天数需要大于等于1'));
                }
                callback();
            },
        },
    ],
    last_patient_report_audit_days: [
        {
            required: false,
            trigger: 'change',
            validator: (rule, value, callback) => {
                if (!formData.value.last_patient_report_audit_days_compare !== !value) {
                    callback(new Error('请完整填写！'));
                }
                if (value && +value < 1) {
                    callback(new Error('填写的天数需要大于等于1'));
                }
                callback();
            },
        },
    ],
    last_bjh_publish_days: [
        {
            required: false,
            trigger: 'change',
            validator: (rule, value, callback) => {
                if (!formData.value.last_bjh_publish_days_compare !== !value) {
                    callback(new Error('请完整填写！'));
                }
                if (value && +value < 1) {
                    callback(new Error('填写的天数需要大于等于1'));
                }
                callback();
            },
        },
    ],
    last_bjh_img_text_publish_days: [
        {
            required: false,
            trigger: 'change',
            validator: (rule, value, callback) => {
                if (!formData.value.last_bjh_img_text_publish_days_compare !== !value) {
                    callback(new Error('请完整填写！'));
                }
                if (value && +value < 1) {
                    callback(new Error('填写的天数需要大于等于1'));
                }
                callback();
            },
        },
    ],
    last_bjh_short_video_publish_days: [
        {
            required: false,
            trigger: 'change',
            validator: (rule, value, callback) => {
                if (!formData.value.last_bjh_short_video_publish_days_compare !== !value) {
                    callback(new Error('请完整填写！'));
                }
                if (value && +value < 1) {
                    callback(new Error('填写的天数需要大于等于1'));
                }
                callback();
            },
        },
    ],
    last_bjh_small_video_publish_days: [
        {
            required: false,
            trigger: 'change',
            validator: (rule, value, callback) => {
                if (!formData.value.last_bjh_small_video_publish_days_compare !== !value) {
                    callback(new Error('请完整填写！'));
                }
                if (value && +value < 1) {
                    callback(new Error('填写的天数需要大于等于1'));
                }
                callback();
            },
        },
    ],
    last_bjh_dynamic_publish_days: [
        {
            required: false,
            trigger: 'change',
            validator: (rule, value, callback) => {
                if (!formData.value.last_bjh_dynamic_publish_days_compare !== !value) {
                    callback(new Error('请完整填写！'));
                }
                if (value && +value < 1) {
                    callback(new Error('填写的天数需要大于等于1'));
                }
                callback();
            },
        },
    ],
    fudan: [
        {
            trigger: 'change',
            validator: (rule, value, callback) => {
                // 一个值存在，一个值不存在时
                if (!formData.value.fudan_all_department_rank !== !formData.value.fudan_all_department_rank_compare) {
                    callback(new Error('复旦排名必须填写完整！'));
                }
                callback();
            },
        },
    ],
    auth_type: [],
});

const configListMap = ref<SelectItems>({
    field_items: [],
    rigit_items: [],
    hospital_level_items: [],
    clinical_title_items: {},
});

const showDoctorConfig = computed(() => {
    const { user_range, user_regist_type } = formData.value;
    const showSettled = user_range === '1';
    const showRegister = showSettled && user_regist_type === '1';
    const showUnregister = showSettled && user_regist_type === '0';
    const showTitle = !!Object.keys(configListMap.value.clinical_title_items).length;
    return {
        showSettled,
        showRegister,
        showUnregister,
        showTitle,
    };
});

const handleFunctionRightsChange = rights_str => {
    formData.value.rights_str = rights_str;
};

const defaultRightsNew = computed(() => {
    const rightsNew = formData.value?.rights_str;

    return {
        include: rightsNew?.include || [],
        exclude: rightsNew?.exclude || [],
    };
});

// 获取筛选项
const getSelectItems = async () => {
    const { errno, data }  = await axios.get<void, BaseReponse<SelectItems>>(Api.doctorAllSelectList);
    if (errno === 0) {
        configListMap.value.rigit_items = data.rigit_items;
        configListMap.value.field_items = data.field_items;
        configListMap.value.forbidden_status_options = data.forbidden_status_options;
        configListMap.value.doctor_acc_info_options = data.doctor_acc_info_options;
        configListMap.value.hospital_level_items = data.hospital_level_items;
        configListMap.value.company_property_options = data.company_property_options;
        configListMap.value.clinical_title_items_relation = data.clinical_title_items_relation;
        configListMap.value.user_login_options = data.user_login_options;
        configListMap.value.real_name_status_options = data.real_name_status_options;
        configListMap.value.rights_items = data.rights_items;
    }
};
getSelectItems();

watch(() => formData.value.user_type, val => {
    // 根据勾选的职业，显示职称选项
    const newClinicalTitleItems = val?.reduce((newTitleItems, occupation) => {
        return Object.assign(newTitleItems, configListMap.value.clinical_title_items_relation[occupation]);
    }, {}) || {};
    configListMap.value.clinical_title_items = newClinicalTitleItems;
});
// 城市组件
const cityTree = ref();
// 医院城市勾选
const cityChange = city => {
    formData.value.city_range = city[0].title?.split(',');
};
// 清空城市选项
const handleClearCity = () => {
    formData.value.city_range = [];
    nextTick(() => {
        // @ts-ignore
        cityTree?.value.change();
    });
};

// 执业范围
const deptsTree = ref();
// 职业科室勾选
const practiceChange = cids => {
    formData.value.std_practice_depts = cids[0].title?.split(',');
};
// 清空执业范围
const handleClearPractice = () => {
    formData.value.std_practice_depts = [];
    defaultDepts.value = [];
};

// 医院科室
const cidTree = ref();
// 医院科室勾选
const cidChange = cids => {
    formData.value.cid2 = cids[0].value?.split(',');
    formData.value.cidTitle = cids[0].title?.split(',');
};
// 清空医院科室
const handleClearCids = () => {
    formData.value.cidTitle = [];
    formData.value.cid2 = [];
    nextTick(() => {
        // @ts-ignore
        cidTree?.value.change();
    });
};

// 单选点击取消
const unCheck = (targetValue, formDataKey) => {
    // 如果点击的是当前选中项，就将其置空
    if (targetValue === formData.value[formDataKey]) {
        formData.value[formDataKey] = '';
    }
};

// 处理提交数据
// const getParams = () => {
//     let params:Record<string, any> = {};
//     // 全部筛选 为undefind 或者 '0' 或者 没有勾选用户入驻
//     if (!formData.value.user_range || formData.value.user_range === '0' || !formData.value.user_regist_type) {
//         params.user_range = formData.value.user_range;
//     } else if (formData.value.user_regist_type === '1') {
//         // 入驻用户
//         util.clone(params, formData.value);
//     } else if (formData.value.user_regist_type === '0') {
//         // 未入驻用户
//         params = {
//             user_range: formData.value.user_range,
//             is_login: formData.value.is_login,
//             real_name_status: formData.value.real_name_status,
//             forbidden_end: formData.value.forbidden_end,
//             doctor_acc_info: formData.value.doctor_acc_info,
//         };
//     }
//     return params;
// };

const doctorCategoryForm = ref();
const emits = defineEmits(['submit']);
const submit = () => {
    doctorCategoryForm.value.validate(async vaild => {
        if (vaild) {
            const hospitals = [];
            hosp.value.forEach(el => {
                hospitalOptionsList.value.forEach(item => {
                    if (el === item.area_id) {
                        hospitals.push(
                            {
                                'company_area_id': parseInt(item.area_id, 10),
                                'area_name': item.area_name,
                            }
                        );
                    }
                });
            });
            formData.value.hospitals = hospitals;
            emits('submit', normalizedDoctorCategoryParams(formData.value, props.id));
        }
    });
};


const reset = () => {
    formData.value = {
        user_range: '0', // 全部筛选 0 筛选用户 1
        user_regist_type: '', // 入驻
        is_exist_doc_id: '',
        // 认证信息
        user_type: [], // 职业
        auth_type: [], // 认证范围
        forbidden_end: [], // 封禁状态 1 永久 2 临时 3 未禁用
        acc_delete_status: [], // 注销状态 1 注销 0 未注销
        // 个人信息
        company_grade: [], // 医院等级
        city_range: [], // 医院城市
        company_property: [], // 医院性质 0数据源异常或未获取 1公立 2民营 3未知
        hospitals: [], // 医院
        fudan_all_department_rank_compare: '', // 大于，大于等于，等于，小于，小于等于
        fudan_all_department_rank: '', // 复旦排名节点 与这个值相比较
        specialty_hospital: '', // 专科名称
        specialty_hospital_compare: '', // 大于，大于等于，等于，小于，小于等于
        // specialty_hospital_rank: '', // 专科排名节点 与这个值相比较
        clinical_title: [], // 职称
        std_practice_depts: [], // 执业科室
        cid2: [], // 医院科室
        cidTitle: [], // 医院科室名称
        // 权益信息
        rights_bit: [], // 权益标签
        // 运营标记
        is_concentrate: '', // 精选医生
        is_self: '', // 自雇医生
        is_medical_cosmetology: '', // 医美医生
        // 用户行为
        new_regist_days: '', // 新入驻天数
        no_active_day: '', // 未活跃天数
        last_consult_claim_days_compare: '', // 未活跃时长比较 大于，大于等于，等于，小于，小于等于
        last_consult_claim_days: '', // 未活跃天数
        last_jh_order_days_compare: '', // 未活跃时长比较 大于，大于等于，等于，小于，小于等于
        last_jh_order_days: '', // 未活跃天数
        last_patient_report_audit_days_compare: '', // 未活跃时长比较 大于，大于等于，等于，小于，小于等于
        last_patient_report_audit_days: '', // 未活跃天数
        last_bjh_publish_days_compare: '', // 未活跃时长比较 大于，大于等于，等于，小于，小于等于
        last_bjh_publish_days: '', // 未活跃天数
        last_bjh_img_text_publish_days_compare: '', // 未活跃时长比较 大于，大于等于，等于，小于，小于等于
        last_bjh_img_text_publish_days: '', // 未活跃天数
        last_bjh_short_video_publish_days_compare: '', // 未活跃时长比较 大于，大于等于，等于，小于，小于等于
        last_bjh_short_video_publish_days: '', // 未活跃天数
        last_bjh_small_video_publish_days_compare: '', // 未活跃时长比较 大于，大于等于，等于，小于，小于等于
        last_bjh_small_video_publish_days: '', // 未活跃天数
        last_bjh_dynamic_publish_days_compare: '', // 未活跃时长比较 大于，大于等于，等于，小于，小于等于
        last_bjh_dynamic_publish_days: '', // 未活跃天数
        diy_uids: '', // 自定义uid
        diy_operation: '', // 自定义uid 的运算关系 1 交集 2 并集 3 补集
        // 未入驻的
        is_login: '', // 登录
        real_name_status: '', // 实名认证  1： 初级实名   2： 高级实名
        new_regist_days_compare: '', // 入驻时长比较 大于，大于等于，等于，小于，小于等于
        no_active_day_compare: '', // 不活跃时长比较 大于，大于等于，等于，小于，小于等于
    };
};

defineExpose({
    reset,
});
</script>
<style lang="less" scoped>
.doctor-category-form {
    min-height: 600px;
}
.ivu-form-item {
    margin-bottom: 20px;
}
.form-item-checkbox-over {
    display: flex;
    /deep/ .ivu-form-item-label {
        flex-shrink: 0;
    }
}
.regist-box {
    vertical-align: baseline;
}
.close-btn{
    margin-left: 6px;
    cursor: pointer;
}
</style>
