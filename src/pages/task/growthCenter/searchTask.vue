<template>
    <div>
        <i-select
            v-model="selectValue"
            filterable
            :placeholder="placeholder"
            label-in-value
            :multiple="multiple"
            :remote-method="remoteSearch"
            :loading="loading"
            :disabled="disabled"
            style="width:200px"
            @on-change="handleChange"
        >
            <i-option
                v-for="(option, i) in visitTaskList"
                :key="i"
                :value="option.value"
            >{{ option.label }}</i-option>
        </i-select>
    </div>
</template>
<script setup lang="ts">
import Api from '@/api/task';
import axios from 'axios';
import { ref, computed, watch } from 'vue';
import { transformData } from '../taskList/utils';
import lodash from 'lodash';
import type { BaseReponse, List } from '@/interfance/Base';

interface TaskSearchProps {
    value: number | number[] | string | string[];
    multiple?: boolean;
    placeholder?: string;
    defaultOptions?: Array<TaskOption>
    status?: number;
    disabled?: boolean;
}

interface TaskOption {
    label: string;
    value: number;
}

interface TaskSearchParams {
    taskID?: number;
    taskName?: string;
    pageSize: number;
    status?: number;
}

interface TaskListItem {
    taskID: number;
    taskName: string;
}

const props = withDefaults(defineProps<TaskSearchProps>(), {
    value: '',
    multiple: false,
    placeholder: '请输入任务名称或ID',
    defaultOptions: () => ([]),
    status: 0,
    disabled: false,
});

const emit = defineEmits(['input', 'change']);

const selectValue = computed({
    get() {
        return props.value;
    },
    set(value) {
        emit('input', value);
    },
});

const visitTaskList = ref<Array<TaskOption>>([]);


watch(() => props.value, (newValue, oldValue) => {
    if (props.multiple) {
        visitTaskList.value = props?.defaultOptions || [];
    } else if (!oldValue && newValue) {
        remoteSearch(newValue);
    }
}, {
    immediate: true,
});

const loading = ref(false);

const remoteSearch = lodash.throttle(async (search: string) => {
    if (!search) {
        visitTaskList.value = [];
        return;
    };
    const params : TaskSearchParams = { pageSize: 200 };
    if (props.status) {
        params.status = props.status;
    };
    if (+search) {
        params.taskID = +search;
    } else {
        params.taskName = search;
    }
    loading.value = true;

    const { errno, data } = await axios.post<void, BaseReponse<List<TaskListItem>>>(Api.tasknewlist, {
        ...transformData(params),
    });

    if (errno === 0) {
        visitTaskList.value = data.list ? data.list.map(el => ({
            label: el.taskName,
            value: el.taskID,
        })) : [];
        loading.value = false;
    }
});

const handleChange = (labelOrValueArr: Array<TaskOption> | TaskOption) => {
    emit('change', labelOrValueArr);
};
</script>
