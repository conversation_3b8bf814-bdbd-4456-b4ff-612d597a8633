interface ModuleInfo {
    moduleID: number;
    moduleName: string;
    moduleDesc: string;
}

type ModuleMapType = Record<number, ModuleInfo>;

const PRODUCTION_MODULES: ModuleMapType = {
    1: { moduleID: 277677, moduleName: 'lv1', moduleDesc: '成长体系lv1' },
    2: { moduleID: 277676, moduleName: 'lv2', moduleDesc: '成长体系lv2' },
    3: { moduleID: 277674, moduleName: 'lv3', moduleDesc: '成长体系lv3' },
    4: { moduleID: 277673, moduleName: 'lv4', moduleDesc: '成长体系lv4' },
    5: { moduleID: 277672, moduleName: 'lv5', moduleDesc: '成长体系lv5' },
    6: { moduleID: 277671, moduleName: 'lv6', moduleDesc: '成长体系lv6' },
    7: { moduleID: 277664, moduleName: 'lv7', moduleDesc: '成长体系lv7' },
};

const DEVELOPMENT_MODULES: ModuleMapType = {
    1: { moduleID: 56964, moduleName: 'lv1', moduleDesc: '成长体系lv1' },
    2: { moduleID: 56963, moduleName: 'lv2', moduleDesc: '成长体系lv2' },
    3: { moduleID: 56962, moduleName: 'lv3', moduleDesc: '成长体系lv3' },
    4: { moduleID: 56961, moduleName: 'lv4', moduleDesc: '成长体系lv4' },
    5: { moduleID: 56960, moduleName: 'lv5', moduleDesc: '成长体系lv5' },
    6: { moduleID: 56959, moduleName: 'lv6', moduleDesc: '成长体系lv6' },
    7: { moduleID: 56958, moduleName: 'lv7', moduleDesc: '成长体系lv7' },
};

const getModuleMap = (): ModuleMapType => {
    // 线上线下环境判断
    return CNAP_ENV === 'test'
        ? PRODUCTION_MODULES
        : DEVELOPMENT_MODULES;
};

export const ModuleMap = getModuleMap();