<template>
    <div>
        <div class="headWrap">
            <iTitle :title="`医疗账户总成长值：${totalSum}   医美账户总成长值：${medical_cosmetology_totalSum }`" />
            <div>
                <i-button
                    type="primary"
                    shape="circle"
                    @click="() => $refs.recordlist.open()"
                >发放执行记录</i-button>
                <i-button
                    slot="export"
                    type="primary"
                    shape="circle"
                    @click="setMultiGrowth"
                >批量下发成长值</i-button>
            </div>
        </div>
        <div v-show="searchFields.length" class="search">
            <base-search
                :search-list="searchList"
                :search-fields="searchFields"
                @onSubmit="onSearch"
            >
                <i-button
                    slot="extraAction"
                    type="success"
                    shape="circle"
                    @click="exportList"
                >导出</i-button>
                <i-button
                    slot="export"
                    type="primary"
                    shape="circle"
                    :disabled="!searchList.uid"
                    @click="setGrowthDist"
                >调整成长值</i-button>
            </base-search>
            <i-table
                :loading="listLoading"
                :columns="growthColumns"
                :data="growthList"
            />
            <div class="page">
                <i-page
                    :total="total"
                    :current="curPage"
                    :page-size="pageSize"
                    flex="main:center"
                    show-total
                    @on-change="changePage"
                />
            </div>
        </div>
        <i-modal
            v-model="setGrowth"
            title="设置成长值"
            @on-visible-change="handleVisibleChange"
        >
            <div class="contentWrap">
                <span class="title">成长值类型</span>
                <span class="content">
                    <i-select v-model="distribute.domainId" transfer>
                        <i-option
                            v-for="(opt, idx) in domainList"
                            :key="idx"
                            :value="opt.value"
                            :label="opt.label"
                        />;
                    </i-select>
                </span>
            </div>
            <div v-if="distribute.opType !== 1" class="contentWrap">
                <span class="title">成长值数量</span>
                <span class="content">
                    <i-input
                        v-model="distribute.expScoreNum"
                        style="width:50px"
                        value="1"
                    />
                </span>
            </div>
            <div class="contentWrap">
                <span class="title">是否关联任务</span>
                <i-select
                    v-model="distribute.isRelation"
                    class="c-gap-right-small"
                    transfer
                    style="width: 100px"
                >
                    <i-option :value="1">是</i-option>
                    <i-option :value="2">否</i-option>
                </i-select>
                <search-task
                    v-show="distribute.isRelation === 1" v-model="distribute.taskID"
                    @change="taskChange"
                />
            </div>
            <div v-show="distribute.isRelation === 2" class="contentWrap">
                <span class="title">成长值来源</span>
                <span class="content">
                    <i-input
                        v-model="distribute.sourceName"
                        type="textarea"
                        show-word-limit
                        maxlength="12"
                        style="width:200px"
                        placeholder="限制输入12个字"
                    />
                </span>
            </div>
            <div v-if="distribute.opType === 1" class="contentWrap">
                <span class="title">下发人群及成长值</span>
                <span class="content" style="width:300px">
                    <p>
                        <i-upload
                            action="#"
                            accept=".csv"
                            :before-upload="beforeUpload"
                            show-upload-list
                            style="display:inline-block;margin-right:5px"
                        >
                            <i-button icon="ios-cloud-upload-outline">上传文件</i-button>
                        </i-upload>
                        <i-button icon="ios-download-outline" @click="downTepCsv">下载模板</i-button>
                    </p>
                    <p>{{ csvName }}</p>
                </span>
            </div>
            <div class="contentWrap">
                <span class="title">备注</span>
                <span class="content">
                    <i-input
                        v-model="distribute.description"
                        placeholder="限制文本50字"
                        type="textarea"
                    />
                </span>
            </div>
            <div v-if="distribute.opType === 1" class="contentWrap">
                <span style="color:rgba(255, 0, 0, 0.769)">提示：用户uid、成长值变动数量为必填字段，不填写发上传成功；如果绑定任务，成长值来源字段展示的是任务名称</span>
            </div>
            <div slot="footer">
                <i-button type="default" @click="() => setGrowth = false">取消</i-button>
                <i-button type="primary" @click="submitGrowthDist">确认</i-button>
            </div>
        </i-modal>
        <execute-record ref="recordlist" />
    </div>
</template>

<script step>
import dayjs from 'dayjs';
import Api from '@/api/task';
import iModal from '@components/modal';
import iButton from '@components/button';
import iTable from '@components/table';
import iPage from '@components/page';
import utils from '@/utils/util';
import UidSearch from '../../BusComponents/mz-uid-search/index.vue';
import searchTask from './searchTask.vue';
import executeRecord from './executeRecord.vue';
import IUpload from '@components/upload';
import { getGrowthDetailList, setGrowthDistribute, getBenefitsSelected } from './api';
import { uploadBos } from '@/utils/uploadBoss';
export default {
    components: {
        iModal,
        iButton,
        iTable,
        iPage,
        searchTask,
        IUpload,
        executeRecord,
    },
    data() {
        this.growthColumns = [
            {
                key: 'expScoreType',
                title: '成长值类型',
                align: 'center',
                minWidth: 150,
            },
            {
                key: 'taskName',
                title: '成长值条件',
                align: 'center',
                minWidth: 150,
            },
            {
                key: 'expScore',
                title: '成长值变动数量',
                align: 'center',
                minWidth: 150,
            },
            {
                key: 'updateTime',
                title: '更新时间',
                align: 'center',
                minWidth: 200,
            },
            {
                key: 'issueChannel',
                title: '下发渠道',
                align: 'center',
                minWidth: 200,
            },
        ];
        return {
            listLoading: false,
            editFlag: false,
            editContent: {},
            searchList: {},
            searchFields: [],
            growthList: [],
            setGrowth: false,
            totalSum: 0,
            medical_cosmetology_totalSum: 0,
            total: 0,
            curPage: 1,
            pageSize: 10,
            domainList: [],
            growthValList: [
                '参与项目发放成长值',
                '参与活动发放成长值',
                '完成任务补发成长值',
                '完成任务发放成长值',
                '发放成长值通知',
                '作弊扣减成长值',
                '扣减成长值通知',
                '触发平台规则扣减成长值',
            ],
            distribute: {
                domainId: null,
                expScoreNum: 1,
                uids: '',
                description: '',
                packageID: 0,
                fid: '',
                taskName: '',
                taskID: '',
                isRelation: 1,
                opType: 0,
                sourceName: '',
            },
            csvName: '',
        };
    },
    watch: {
        pageSize(newVal) {
            this.searchList.pageSize = newVal;
            this.getList();
        },
        curPage(newVal) {
            this.searchList.pageNum = newVal;
            this.getList();
        },
    },
    methods: {
        taskChange(labalOrVal) {
            this.distribute.taskName = labalOrVal.label;
        },
        async getSelectItem() {
            const { errno, data } = await getBenefitsSelected();
            if (errno === 0) {
                this.domainList = data?.rel_type?.map(item => ({
                    label: item.name,
                    value: item.domain_id,
                }));
                this.searchFields = [...this.searchFields];
            }
        },
        handleVisibleChange(tag) {
            if (!tag) {
                this.resetGrowthDist();
            };
        },
        beforeUpload(file) {
            this.handleBosUpload(file);
            return false;
        },
        async handleBosUpload(file) {
            const { order_id } = await uploadBos(file);
            this.distribute.fid = order_id;
            this.csvName = file.name;
        },
        downTepCsv() {
            const link = document.createElement('a');
            link.href = Api.downadjustgrowthtpl;
            link.download = '';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },
        async getList() {
            this.listLoading = true;
            const data = await getGrowthDetailList(this.searchList);
            this.total = data.total;
            this.totalSum = data.totalSum || 0;
            this.medical_cosmetology_totalSum = data.medical_cosmetology_totalSum || 0;
            this.growthList = data.list;
            if (data.list[0]) {
                this.distribute.packageID = data.list[0].packageID;
            }
            this.listLoading = false;
        },
        changeDate(date) {
            this.searchList.updateTime = new Date(date).getTime() / 1000;
        },
        setGrowthDist() {
            this.distribute.opType = 0;
            this.distribute.uids = this.searchList.uid;
            this.setGrowth = true;
        },
        changePage(page) {
            this.curPage = page;
        },
        onSearch() {
            this.getList();
        },
        exportList() {
            utils.openNewUrl(Api.exportGrowthDetail, {
                ...this.searchList,
                export: 1,
            });
        },
        resetGrowthDist() {
            this.distribute = {
                domainId: 0,
                expScoreNum: 1,
                uids: '',
                description: '',
                packageID: 0,
                fid: '',
                taskName: '',
                taskID: '',
                isRelation: 1,
                opType: 0,
                sourceName: '',
            };
            this.csvName = '';
        },
        async submitGrowthDist() {
            if (this.distribute.description.length > 50) {
                this.$Message.error('备案字数不能超过50个字');
                return;
            };

            const { isRelation, ...params } = this.distribute;

            if (isRelation === 1) {
                params.sourceName = '';
            } else {
                params.taskName = '';
                params.taskID = '';
            };


            const { errno, errmsg } = await setGrowthDistribute(params);

            if (errno === 0) {
                this.$Message.success('提交成功');
                this.setGrowth = false;
            }
        },
        setMultiGrowth() {
            this.distribute.opType = 1;
            this.distribute.uids = '';
            this.setGrowth = true;
        },
        uidChange(value) {
            this.searchList = {
                ...this.searchList,
                uid: value,
            };
        },
        onClear() {
            this.searchList = {
                ...this.searchList,
                uid: '',
            };
        },
    },
    async mounted() {
        await this.getSelectItem();
        this.searchList = {
            uid: this.$route.query.uid || '',
            domainId: '',
            issueChannel: '',
            updateTime: '',
            pageSize: this.pageSize,
            pageNum: this.curPage,
        };
        this.searchFields = [
            {
                key: 'uid',
                type: 'extra',
                render: h => (
                    <UidSearch
                        style="margin-right: 0px;"
                        default-uid={this.$route.query.uid}
                        onOnSearch={this.uidChange}
                        onOnClear={this.onClear}
                    />
                ),
            },
            {
                key: 'domainId',
                type: 'select',
                placeholder: '成长值类型',
                optionList: this.domainList,
            },
            {
                type: 'extra',
                render: h => (
                    <date-picker
                        type="date"
                        on-on-change={this.changeDate}
                        placeholder="更新时间"
                        style="width: 200px"
                        format="yyyy-MM-dd"
                    ></date-picker>
                ),
            },
            {
                key: 'issueChannel',
                type: 'select',
                placeholder: '下发渠道',
                optionList: [
                    { label: '系统', value: 1 },
                    { label: '人工', value: 2 },
                ],
            },
        ];
        this.searchList.uid && this.getList();
    },
};
</script>
<style lang="less" scoped>
.headWrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.search {
    margin-bottom: 20px;
}
.contentWrap {
    display: flex;
    align-items: center;
    justify-content: start;
    margin: 10px 0;
    .title {
        display: block;
        width: 110px;
        text-align: left;
        margin-right: 10px;
    }
    .content {
        width: 200px;
        text-align: left;
    }
}
.page {
    margin-top: 10px;
}
</style>
