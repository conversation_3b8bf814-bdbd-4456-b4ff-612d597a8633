<template>
    <i-tab-pane :label="tabContent.label">
        <i-table
            v-if="isTotalExp || isYm"
            border
            width="652"
            :columns="growthColumns"
            :data="growthList"
            :loading="loading"
        />
        <div v-if="isYm">
            <div class="growWrap">
                <div>
                    设置升成长值条件
                </div>
                <div>
                    <i-button
                        v-show="!isSorting && growModalList.length"
                        type="info"
                        class="c-gap-right-small"
                        @click="editSorting(true)"
                    >编辑模块顺序</i-button>
                    <i-button
                        type="primary"
                        :disabled="isSorting"
                        @click="createModal"
                    >+创建模块</i-button>
                </div>
            </div>
            <draggable
                v-model="growModalList"
                :animation="100"
                scroll-sensitivity="200"
                :options="{
                    handle: '.dragger-icon',
                }"
                @start="onDragHandle(true)"
                @end="onDragHandle(false)"
            >
                <i-set-growth-condition
                    v-for="(modal, key) in growModalList"
                    :key="modal.timestamp"
                    ref="allGrowth"
                    :content="modal"
                    :idx="key"
                    :activity-id="tabContent.msId"
                    :is-out-drug="isOutDrug"
                    :is-sorting="isSorting"
                    :is-ym="isYm"
                    @statusChange="statusChange"
                    @delModal="delModal"
                    @refreshModal="refreshModal"
                    @sortSubmit="sortSubmit"
                />
            </draggable>
        </div>
        <i-set-growth-condition
            v-if="!isYm && !isTotalExp"
            :content="growModalList[0]"
            :is-ym="isYm"
            :activity-id="tabContent.msId"
            @refreshModal="refreshModal"
        />
        <div
            v-show="isSorting"
            class="c-gap-top-large"
        >
            <i-button
                class="c-gap-right"
                @click="cancelSort"
            >取消</i-button>
            <i-button
                type="primary"
                @click="sortSubmit"
            >保存</i-button>
        </div>
    </i-tab-pane>
</template>

<script>
import iTabPane from '@components/tab-pane';
import iTable from '@components/table';
import { getGrowthList, postEditOrder } from './api';
import iSetGrowthCondition from './setGrowthCondition';
import draggable from 'vuedraggable';
export default {
    name: 'business',
    components: {
        iTabPane,
        iTable,
        iSetGrowthCondition,
        draggable,
    },
    props: {
        tabContent: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        this.MAXLEVEL = 8; // 最高等级
        this.growthColumns = [
            {
                key: 'level',
                title: '等级',
                align: 'center',
                width: 150,
            },
            {
                key: 'title',
                title: '等级名称',
                align: 'center',
                width: 150,
            },
            {
                key: 'intervalText',
                title: '成长值区间',
                align: 'center',
                width: 150,
                render: (h, { row }) => {
                    if (row.level === this.MAXLEVEL) {
                        return h(
                            'span',
                            `≥${row.start_num}`
                        );
                    }
                    return h(
                        'span',
                        `${row.start_num}-${row.end_num}`
                    );
                },
            },
            {
                title: '操作',
                key: 'status',
                align: 'center',
                width: 200,
                render: (h, { row }) => {
                    return h(
                        'a',
                        {
                            on: {
                                click: () => {
                                    this.$emit('editLevel', row);
                                },
                            },
                        },
                        '编辑信息'
                    );
                },
            },
        ];
        return {
            growthList: [],
            growModalList: [],
            isOutDrug: false,
            isSorting: false, // 排序中状态
            canSort: true, // 是否可编辑排序
            loading: false,
        };
    },
    watch: {
        tabContent: {
            immediate: true,
            handler(newVal) {
                newVal.msId && this.fetchGrowthList();
            },
        },
    },
    computed: {
        isYm() {
            return this.tabContent.domain === '医美成长体系';
        },
        // 晋级总分
        isTotalExp() {
            return this.tabContent.label === '晋级总分';
        },
    },
    methods: {
        statusChange(val) {
            if (val) {
                this.canSort = val;
                return;
            };
            this.canSort = this.$refs.allGrowth?.every(item => item.editStatus === 0)
                && this.growModalList.every(item => item.flag !== 'create');
        },
        cancelSort() {
            this.refreshModal();
            this.editSorting(false);
        },
        getSortParams(newTitleName) {
            const orderList = this.growModalList?.map((item, i) => {
                return {
                    titleName: item.titleName ?? newTitleName,
                    order: i + 1,
                };
            }) || [];
            return {
                msId: this.tabContent.msId,
                orderList,
            };
        },
        async sortSubmit(newTitleName) {
            const { errno, data } = await postEditOrder(this.getSortParams(newTitleName));
            if (errno === 0) {
                this.editSorting(false);
                this.$Message.success('保存成功');
                this.refreshModal();
            }
        },
        onDragHandle(dragStatus) {
            this.isOutDrug = dragStatus;
        },
        editSorting(val) {
            if (!this.canSort && val) {
                this.$Message.info('请保存模块后再进行排序');
                return;
            }
            this.isSorting = val;
        },
        async fetchGrowthList() {
            this.loading = true;
            const data = await getGrowthList({
                msId: this.tabContent.msId,
            });
            if (data) {
                this.growthList = data?.list || [];
                this.$nextTick(() => {
                    this.growModalList = data?.growthConditionList || [];
                });
            }
            this.loading = false;
        },
        createModal() {
            const isCreate = this.growModalList?.some(item => item.flag === 'create');
            if (isCreate) {
                this.$Message.info('请先保存正在创建的模块');
                return;
            }
            const tmp = this.growModalList.slice();
            tmp.unshift({
                flag: 'create',
            });
            this.growModalList = [];
            this.$nextTick(() => {
                this.growModalList = tmp;
            });
        },
        delModal(id) {
            const tmp = this.growModalList.slice();
            tmp.splice(id, 1);
            this.growModalList = [];
            this.$nextTick(() => {
                this.growModalList = tmp;
            });
        },
        refreshModal() {
            this.fetchGrowthList();
        },
    },
};
</script>
<style lang="less" scoped>
.growWrap {
    width: 952px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
}
</style>

