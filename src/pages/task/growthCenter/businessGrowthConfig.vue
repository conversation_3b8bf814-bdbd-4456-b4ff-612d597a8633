<template>
    <div>
        <i-radio-group
            v-model="tabType"
            type="button"
            size="large"
            class="tabWrap"
            @on-change="getConfigList"
        >
            <i-radio
                v-for="item in tabConfig"
                :key="item.name"
                :label="item.name"
            >{{ item.label }}</i-radio>
        </i-radio-group>
        <i-tabs
            type="card"
            class="c-gap-top-large"
            :animated="false"
        >
            <i-business
                v-for="(tab, key) in tabList"
                ref="tabs"
                :key="key"
                :tab-content="tab"
                @editLevel="editLevel"
            />
        </i-tabs>
        <i-modal
            v-model="editFlag"
            title="成长等级编辑"
        >
            <i-form
                ref="editForm"
                :model="editContent"
                :label-width="120"
                label-position="left"
                :rules="rules"
            >
                <form-item label="等级">
                    <span>{{ editContent.level }}</span>
                </form-item>
                <form-item label="等级命名">
                    <span>{{ editContent.title }}</span>
                </form-item>
                <form-item label="成长值区间">
                    <i-input v-model="editContent.start_num" style="width: 100px" />
                    <span class="c-gap-left c-gap-right">
                        {{ editContent.end_num > 0 ? (editContent.level === 8 ? '>=' : '-') : '' }}
                    </span>
                    <span
                        v-if="editContent.end_num > 0"
                        style="height: 32px;line-height: 32px;"
                    >{{ editContent.end_num }}</span>
                </form-item>
                <div v-if="isYl">
                    <form-item label="必做任务" prop="isNonAutoUp">
                        <i-switch
                            v-model="editContent.isNonAutoUp"
                            :true-value="1"
                            :false-value="0"
                            @on-change="isNonAutoUpChange"
                        >
                            <span slot="open">是</span>
                            <span slot="close">否</span>
                        </i-switch>
                    </form-item>
                    <form-item label="成长任务" prop="taskIds">
                        <search-task
                            v-model="editContent.taskIds"
                            multiple
                            :disabled="editContent.isNonAutoUp === 0"
                            :default-options="defaultTaskList"
                            @change="taskIdsChange"
                        />
                    </form-item>
                </div>
            </i-form>
            <div slot="footer">
                <i-button type="primary" @click="editGrowth">确定</i-button>
                <i-button @click="editFlag = false">取消</i-button>
            </div>
        </i-modal>
    </div>
</template>

<script>
import ApiNr from '@/api/nr';
import iTabs from '@components/tabs';
import iRadioGroup from '@components/radio-group';
import iRadio from '@components/radio';
import { directive as viewer } from 'v-viewer';
import iModal from '@components/modal';
import iInput from '@components/input';
import searchTask from './searchTask';
import iBusiness from './business';
import { editGrowthRange, getBenefitsSelected } from './api';
import { ModuleMap } from './data';

export default {
    components: {
        iTabs,
        iBusiness,
        iModal,
        iInput,
        iRadioGroup,
        iRadio,
        searchTask,
    },
    data() {
        return {
            editFlag: false,
            editContent: {
                taskIds: [],
            },
            actionUrl: ApiNr.nrUpload,
            tabType: '医疗',
            tabConfig: [
                {
                    label: '医疗',
                    name: '医疗',
                    tabList: [],
                },
                {
                    label: '医美',
                    name: '医美',
                    tabList: [],
                },
            ],
            tabList: [],
            defaultTaskList: [],
            rules: {
                taskIds: [
                    {
                        required: false,
                        trigger: 'change',
                        validator: (rule, value, callback) => {
                            if (this.isYl && this.editContent.isNonAutoUp === 1 && value.length === 0) {
                                callback(new Error('请填写必做任务'));
                            }
                            else {
                                callback();
                            }
                        },
                    },
                ],
            },
        };
    },
    computed: {
        isYl() {
            return this.tabType === '医疗';
        },
    },
    async created() {
        await this.getSelectItem();
        this.getConfigList(this.tabType);
    },
    methods: {
        isNonAutoUpChange(val) {
            if (val === 0) {
                this.$set(this.editContent, 'tasks', []);
                this.$set(this.editContent, 'taskIds', []);
            }
        },
        taskIdsChange(labelOrVal) {
            labelOrVal = labelOrVal.map(item => ({
                taskId: item.value,
                taskName: item.label,
            }));
            this.$set(this.editContent, 'tasks', labelOrVal);
        },
        getConfigList(tab) {
            const targetObj = this.tabConfig.find(el => el.name === tab);
            if (targetObj) {
                this.tabList = targetObj.tabList;
            };
        },
        async getSelectItem() {
            const { errno, data } = await getBenefitsSelected();
            if (errno === 0) {
                data?.rel_type?.forEach(item => {
                    // 根据 domain 字段判断分类
                    const category = item.domain === '成长体系' ? this.tabConfig[0] : this.tabConfig[1];

                    const tabItem = {
                        ...item,
                        label: item.name,
                        msId: item.ms_inst_id,
                    };

                    // 将新的 tabList 对象添加到对应分类的 tabList 中
                    category.tabList.push(tabItem);
                });
            }
        },
        editLevel(row) {
            this.editFlag = true;
            this.editContent = { ...row };
            if (this.isYl) {
                const taskList = this.editContent.required_task_list || [];
                this.defaultTaskList = taskList.map(item => {
                    return {
                        value: item.parentTaskID,
                        label: item.taskName,
                    };
                }) || [];
                this.$set(this.editContent, 'taskIds', taskList.map(item => item.parentTaskID));
            };
        },
        editGrowth() {
            if (this.editContent.end_num !== 0 && this.editContent.start_num > this.editContent.end_num) {
                this.$Message.error('初始值不能大于最终成长值');
                return;
            }
            if (!this.isYl) {
                this.editGrowthSubmit();
                return;
            }
            this.$refs.editForm.validate(valid => {
                if (valid) {
                    this.editGrowthSubmit();
                }
            });
        },
        async editGrowthSubmit() {
            const { errno } = await editGrowthRange({
                ...this.editContent,
                ...ModuleMap[this.editContent?.level] || {},

            });
            if (errno === 0) {
                this.$Message.success('修改成功');
                this.$refs.tabs.forEach(tab => {
                    tab.fetchGrowthList();
                    tab.editSorting(false);
                });
            };
            this.editFlag = false;
        },
    },
};
</script>

<style lang="less" scoped>
.images {
    width: 50px;
    height: 50px;
}
.tabWrap {
    margin-top: 20px;
}
</style>
