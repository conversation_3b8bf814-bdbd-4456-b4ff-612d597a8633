import axios from 'axios';
import Api from '@/api/task';
import benefitsApi from '@/api/benefits';
import type { BaseReponse } from '@/interfance/Base';

interface GrowthProp {
    levelId: number;
    level: number;
    title: string;
    intervalText: string;
    iconURL: string;
}
interface GrowthList {
    List: GrowthProp[];
}
interface GrowthCondition {
    titleName: string;
    subTitle: string;
    urlValue: string;
    taskList: number[];
    delTaskList?: number[];
}
interface TaskProp {
    taskID: number;
    taskName: string;
    prizeInnerValue: number;
}
interface TaskList {
    list: TaskProp[];
}

interface DetailItem {
    expScore: number;
    taskName: string;
    updateTime: string;
    createUser: string;
    description: string;
    packageID: number;
    growthType: string;
}
interface DetailListProp {
    totalSum: number;
    total: number;
    list: DetailItem[];
}
interface GrowthDistribute {
    failList: [];
}
interface GrowthDetailParams {
    uid: string;
    expScoreType: number;
    update: string;
    createUser: string;
}
interface GrowthDistributeParams {
    expScoreNum: number;
    description: string;
    uids: string;
}
interface SelectItemList {
    rel_type: Reltype;
}
export interface Reltype {
    domain: string;
    domain_id: number;
    dynamic_point_pack_id: number;
    key: string;
    ms_inst_id: number;
    name: string;
}

export interface Benefitslist {
benefitsDefID: number;
title: string;
specification: number;
unit: string;
cost: number;
growth_cost: number;
benefitsConsumer: string[];
}
// 获取成长值列表
export const getGrowthList = async (params): Promise<GrowthList> => {
    const { errno, data } = await axios.post<void, BaseReponse<GrowthList>>(Api.growthList, {
        ...params,
    });
    if (errno === 0) {
        return data;
    }
};

// 编辑成长等级
export const editGrowthRange = async params => {
    return await axios.post<GrowthProp, BaseReponse<object>>(Api.growthedit, {
        ...params,
    });
};

// 设置模块成长值
export const submitGrowthCondition = async params => {
    const { errno, data } = await axios.post<GrowthCondition, BaseReponse<object>>(Api.submitGrowthCondition, {
        ...params,
    });
    if (errno === 0) {
        return data;
    }
};
// 获取成长值条件选项列表
export const getGrowthConditionList = async (params): Promise<TaskList> => {
    const { errno, data } = await axios.get<void, BaseReponse<TaskList>>(Api.getGrwothTaskInfo, {
        ...params,
    });
    if (errno === 0) {
        return data;
    }
};
// 获取成长值明细筛选列表
export const getGrowthDetailList = async (params): Promise<DetailListProp> => {
    const { errno, data } = await axios.post<GrowthDetailParams, BaseReponse<DetailListProp>>(Api.getGrowthDetailList, {
        ...params,
    });

    if (errno === 0) {
        return data;
    }
};

// 批量下发成长值
export const setGrowthDistribute = async (params): Promise<BaseReponse<[]>> => {
    const res = await axios.post<GrowthDistributeParams, BaseReponse<[]>>(Api.setGrowthDistribute, {
        ...params,
    });
    return res;
};

// 筛选项目
export const getBenefitsSelected = async (): Promise<BaseReponse<SelectItemList>> => {
    return axios.get<void, BaseReponse<SelectItemList>>(
        benefitsApi.benefitsselected
    );
};

// 条件排序
export const postEditOrder = (params): Promise<BaseReponse<{}>> => {
    return axios.post<void, BaseReponse<{}>>(
        benefitsApi.modelorder, {
            ...params,
        }
    );
};
