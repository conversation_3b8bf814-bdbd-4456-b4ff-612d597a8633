<template>
    <div>
        <iTitle
            title="任务辅助配置"
            link-url="taskAssistList"
            sub-title="新建"
        />
        <i-form
            ref="formRef"
            :label-width="150"
            :model="formData"
            :rules="ruleValidate"
        >
            <form-item label="任务ID：" prop="taskID">
                <search-task v-model="formData.taskID" :status="0" />
            </form-item>
            <form-item label="浏览时长(s)：" prop="browse_time">
                <input-number
                    v-model="formData.browse_time"
                    style="width:200px;"
                    placeholder="请填写"
                />
            </form-item>
            <form-item label="浏览链接：" prop="material_url">
                <i-input
                    v-model="formData.material_url"
                    style="width: 200px"
                    placeholder="请填写"
                />
            </form-item>
            <form-item label="按钮文案：" prop="button_text">
                <div style="color: #999">{{ formData.button_text }}</div>
            </form-item>
            <form-item>
                <i-button
                    type="default" class="c-gap-right"
                    @click="onCancelList"
                >取消</i-button>
                <i-button type="primary" @click="onSubmit">确认</i-button>
            </form-item>
        </i-form>
    </div>
</template>
<script setup lang="tsx">
import { ref, getCurrentInstance } from 'vue';
import InputNumber from '@components/input-number';
import Message from '@components/message';
import searchTask from '@pages/task/growthCenter/searchTask.vue';
import { addAssistDetail } from './axiosAssist';
import { useRouter } from '@/hooks/router';

const router = useRouter();
const formRef = ref<HTMLFormElement>(null);
const { proxy: ctx }: any = getCurrentInstance();

const formData = ref({
    taskID: '',
    button_text: '阅读并打卡',
    browse_time: '',
    material_url: '',
});

const ruleValidate = {
    taskID: [
        {
            required: true,
            message: '任务ID必须填写',
        },
    ],
    browse_time: [
        {
            required: true,
            message: '浏览时长必须填写',
        },
    ],
    material_url: [
        {
            required: true,
            message: '浏览链接必须填写',
        },
    ],
};

const onCancelList = () => {
    router.push('taskAssistList');
};

const onSubmit = async () => {
    formRef.value.validate(async valid => {
          if (valid) {
              const { errno } = await addAssistDetail({
                  ...formData.value,
                  task_id: formData.value.taskID,
                  create_user: ctx.$store.state.userName,
              });
              if (errno === 0) {
                  Message.success('保存成功');
                  router.push('taskAssistList');
              };
          };
    });
};

</script>
