// @import "./custom";
// @import "./common/index";
// // @import "./color/colors";
// @import "./animation/index";
// @import "./mzmis/index";
@import '~@baidu/venus-ui/src/styles/index.less';
@import "iviewreset";

@primary-color: #5183ff;
@link-color: #5183ff;

.mz-common-title {
    position: relative;
}
.title-h5 {
    height: 44px;
    line-height: 44px;
    color: #17233d;
    font-weight: normal;
    border-bottom: 1px solid #ECECEC;
    margin-bottom: 20px;
    font-size: 14px;
    a {
        color: #17233d;
    }
}
.sub-title-gap {
    background: url(./img/arr2.png) no-repeat;
    background-size: 100%;
    width: 14px;
    height: 14px;
    position: relative;
    display: inline-block;
    top: 2px;
}

