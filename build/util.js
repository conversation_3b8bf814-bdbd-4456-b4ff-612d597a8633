function getIPv4AddressList() {
    const networkInterfaces = require('os').networkInterfaces();
    let result = [];

    Object.keys(networkInterfaces).forEach(key => {
        const ips = (networkInterfaces[key] || []).filter(
                details => details.family === 'IPv4'
            ).map(
                details => details.address.replace('127.0.0.1', 'localhost')
            );
        result = result.concat(ips);
    });
    return result;
}

module.exports = {
    getErrorInfor: function () {
        const port = require('./webpackDevServer.config').port;

        return getIPv4AddressList().map(ip => `http://${ip}:${port}`).toString().replace(/,/g, '\n');
    },
};
