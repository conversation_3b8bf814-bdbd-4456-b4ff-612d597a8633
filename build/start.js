/**
 * @file start.js */
process.on('unhandledRejection', err => {
    console.error(err);
    throw err;
});

process.on('warning', err => {
    console.error(err);
});
const webpack = require('webpack');
const WebpackDevServer = require('webpack-dev-server');
const webpackDevServerConfig = require('./webpackDevServer.config');
const webpackConfigClientDev = require('./webpack.dev.config');
const compiler = webpack(webpackConfigClientDev);
const server = new WebpackDevServer(webpackDevServerConfig, compiler);
server.start();