/**
 * @file 文件打包dll库
 */

const path = require('path');
const webpack = require('webpack');

module.exports = {
    mode: 'development',
    entry: {
        libs: ['vue/dist/vue.esm.js', 'axios', 'vuex', 'vue-router']
    },
    output: {
        path: path.resolve(__dirname, '../dll'),
        filename: '[name].dll.js', // 输出动态连接库的文件名称
        library: '_dll_[name]' // 全局变量名称
    },
    plugins: [
        new webpack.DllPlugin({
            name: '_dll_[name]', // 和output.library中一致，也就是输出的manifest.json中的 name值
            path: path.join(__dirname, '../dll', '[name].manifest.json')
        })
    ]
};
