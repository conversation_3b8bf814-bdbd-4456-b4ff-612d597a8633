/**
 * @file webpackDevServer.config.js */
const openInEditor = require('launch-editor-middleware');
// https://webpack.js.org/configuration/dev-server/
module.exports = {
    hot: true,
    client: {
        reconnect: true,
    },
    port: 8360,
    allowedHosts: 'all', // 不设置 本地改host无法访问
    // liveReload: true, // ps: hot must be false
    // static: {
    //     directory: path.join(__dirname, 'dist'),
    //     serveIndex: true,
    //     watch: {
    //         ignored: '/node_modules/',
    //     },
    // },
    devMiddleware: {
        writeToDisk: true,
    },
    setupMiddlewares(middlewares, { app }) {
        // 指定在那种编辑器中打开组件
        app.use('/__open-in-editor', openInEditor('code'));
        const { mockServiceMiddleware } = require('@baidu/mockable');
        app.use(mockServiceMiddleware());
        return middlewares;
    },
};
