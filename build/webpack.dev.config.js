/**
 * @file webpack.dev.config
 */
const webpack = require('webpack');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const WebpackBar = require('webpackbar');
const { merge } = require('webpack-merge'); ;
const webpackBaseConfig = require('./webpack.base.config.js');
// const fs = require('fs');
const path = require('path');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const root = path.resolve(__dirname, '.');
function resolve(src) {
    return path.join(root, src);
}

module.exports = merge(webpackBaseConfig, {
    optimization: {
        chunkIds: 'named', // https://webpack.js.org/configuration/optimization/#optimizationchunkids
    },
    target: 'web',
    cache: {
        // 将缓存类型设置为文件系统,默认是memory
        type: 'filesystem',
        buildDependencies: {
            // 更改配置文件时，重新缓存
            config: [__filename],
        },
    },
    watch: false,
    watchOptions: {
        // 解决webpack 保存文件编译两次的问题
        // https://github.com/webpack/webpack/issues/15431#issuecomment-1048832781
        aggregateTimeout: 100,
    },
    mode: 'development',
    devtool: 'inline-source-map',
    entry: {
        main: './src/app',
    },
    output: {
        path: resolve('../dist'),
        filename: '[name].js',
        chunkFilename: '[name].chunk.js',
    },
    stats: 'errors-only',
    devServer: {
        disableHostCheck: true,
        // other options
    },
    plugins: [
        new CleanWebpackPlugin(),
        new WebpackBar({
            name: 'muzhi client',
        }),
        new MiniCssExtractPlugin({
            filename: '[name].css',
            ignoreOrder: true,
        }),
        new webpack.DefinePlugin({
            PRODUCTION: JSON.stringify(false),
            PROXY_ENV: JSON.stringify(process.env.PROXY_ENV || 'dev'),
            CNAP_ENV: JSON.stringify(process.env.CNAP_ENV || 'dev'),
        }),
        new HtmlWebpackPlugin({
            filename: 'index.html',
            template: './templates/index.tpl',
            inject: true,
            title: '百度健康',
        }),
    ],
});
