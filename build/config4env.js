
let publicPath =  '/';
console.log('process.env.FCNAP:', process.env.FCNAP);
if (process.env.FCNAP) {
    publicPath = `${process.env.FCNAP_CDN_HOST}/${process.env.FCNAP_CDN_PATH}`;
}
else {
    switch (process.env.CNAP_ENV) {
        case 'production':
            publicPath = `https://muzhi-static.cdn.bcebos.com/fe-mzmis/static/${process.env.AGILE_REVISION}/`;
            break;
        case 'test':
            publicPath = `https://muzhi-static.cdn.bcebos.com/fe-mzmis/static/${process.env.AGILE_REVISION}/`;
            break;
        default:publicPath = '/';
    }

}



module.exports = {
    publicPath: publicPath,
};