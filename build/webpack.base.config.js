/**
 * @file webpack 打包配置
 */
const path = require('path');
const fs = require('fs');
const webpack = require('webpack');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

function resolve(dir) {
    return path.join(__dirname, dir);
}
const { VueLoaderPlugin } = require('vue-loader');
const config4env = require('./config4env');


module.exports = {
    optimization: {
        runtimeChunk: 'single',
        splitChunks: {
            // minSize: 30000,
            // minChunks: 2,
            // maxAsyncRequests: 3,
            // maxInitialRequests: 3,
            // automaticNameDelimiter: '~',
            cacheGroups: {
                libs: {
                    name: 'chunk-libs',
                    test: /[\\/]node_modules[\\/]/,
                    priority: 10,
                    chunks: 'initial', // only package third parties that are initially dependent
                },
                // iviewUI: {
                //     name: 'chunk-iviewUI', // split UI into a single package
                //     priority: 20,
                // the weight needs to be larger than libs and app or it will be packaged into libs or app
                //     test: /\/src\/components/,
                //     chunks: 'async',
                // },
            },
        },
    },
    module: {
        rules: [{ test: /\.vue$/, loader: 'vue-loader' },
            {
                test: /\.(js|ts|jsx|tsx)$/,
                loader: 'babel-loader',
                exclude: /node_modules/,
                include: resolve('../src'),
                options: {
                    cacheDirectory: true,
                },
            },
            {
                test: /\.css$/,
                use: [MiniCssExtractPlugin.loader, 'css-loader'],
            },
            {
                test: /\.less/,
                use: [MiniCssExtractPlugin.loader,
                    {
                        loader: 'css-loader',
                    },
                    {
                        loader: 'less-loader',
                        options: {
                            lessOptions: {
                                javascriptEnabled: true,
                            },
                        },
                    },
                ],
            },
            {
                test: /\.(gif|jpg|png)\??.*$/,
                loader: 'url-loader',
                options: {
                    limit: 1024,
                    esModule: false,
                },
            },
            {
                test: /\.(woff|woff2|svg|eot|ttf)\??.*$/,
                use: [{
                    loader: 'file-loader',
                    options: {
                        esModule: false,
                        publicPath: config4env.publicPath,
                        outputPath: '/',
                    },
                }],
            },
            {
                test: /\.(html|tpl)$/,
                loader: 'html-loader',
            },
        ],
    },
    resolve: {
        extensions: ['.js', '.jsx', '.vue', '.ts', '.tsx'],
        alias: {
            'vue': 'vue/dist/vue.esm.js',
            '@': resolve('../src/'),
            // '@components': resolve('../src/components'),
            '@components': '@baidu/venus-ui/src/components',
            '@BusComponents': resolve('../src/pages/BusComponents'),
            '@utils': resolve('../src/utils'),
            '@pages': resolve('../src/pages'),
            '@directives': resolve('../src/directives'),
        },
    },
    plugins: [
        new VueLoaderPlugin(),
        new webpack.ProvidePlugin({
            '$': 'jquery',
        }),
    ],
};
