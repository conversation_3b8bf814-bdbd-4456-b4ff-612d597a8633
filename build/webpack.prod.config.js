/**
 * @file webpack.prod.config.js
 */
const webpack = require('webpack');
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');

const { merge } = require('webpack-merge'); ;
const webpackBaseConfig = require('./webpack.base.config.js');
const path = require('path');
const PreloadWebpackPlugin = require('preload-webpack-plugin');
const _environment = process.env.NODE_ENV;

const prodPlugins = [
    new MiniCssExtractPlugin({
        filename: '[name].[chunkhash].css',
        ignoreOrder: true,
    }),
    new webpack.DefinePlugin({
        'process.env': {
            NODE_ENV: '"production"',
        },
    }),
    new HtmlWebpackPlugin({
        filename: 'index.tpl',
        template: './templates/index.tpl',
        inject: true,
        title: '百度健康',
    }),
    new webpack.DefinePlugin({
        PRODUCTION: JSON.stringify(true),
        PROXY_ENV: JSON.stringify(process.env.PROXY_ENV || 'online'),
        CNAP_ENV: JSON.stringify(process.env.CNAP_ENV || 'production'),
    }),
    new PreloadWebpackPlugin({
        rel: 'preload',
        as(entry) {
            if (/\.css$/.test(entry)) {
                return 'style';
            }
            if (/\.(woff|svg|eot|ttf)$/.test(entry)) {
                return 'font';
            }
            if (/\.png$/.test(entry)) {
                return 'image';
            }
            return 'script';
        },
        include: 'initial',
    }),
];

if (process.env.use_analyzer) {
    prodPlugins.push(new BundleAnalyzerPlugin());
}

module.exports = merge(webpackBaseConfig, {
    mode: 'production',
    devtool: _environment === 'production' ? 'hidden-source-map' : false,
    entry: {
        app: './src/app',
    },
    output: {
        path: path.join(__dirname, '../dist'),
        filename: '[name].[chunkhash].js',
        publicPath: _environment === 'production' ? 'https://muzhi-static.cdn.bcebos.com/static/mzmis-fe/' : '/static/mzmis-fe/',
        chunkFilename: '[name].[chunkhash].chunk.js',
    },
    optimization: {
        minimize: true,
        minimizer: [
            new CssMinimizerPlugin(),
            new TerserPlugin(),
        ],
    },
    plugins: prodPlugins,

});
