# 目录结构

build // webpack编译相关的配置
dep   // 项目的依赖的npm模块包，一般情况下不需要修改，但是如果项目有安装新的npm包时，需要更新 npm run buildDep
dist  // build产出目录
dll   // webpack的动态打包库，提高本地编译效率，build的没有使用
doc    // 文档
mock   // mock数据相关
 |---config  // mock的配置
 |---service // mock的转发代理相关操作
 |---response // mock的返回数据
src  // 源码
 |---api  // 页面使用的接口，抽离方便统一处理，像mock代理远程等
 |---assets // 项目使用到一些静态资源，像图片，静态网页等
 |---components // 项目的公共组件库，基于iviewui封装
 |---directives // vue的通用指令
 |---mixins // vue的mixins，包含dispatch的实现
 |---pages  // 项目中页面，包含首页，简历管理、职位管理等
 |---plugins // 项目一些通用能力插件，像axios请求预处理
 |---routers // 项目页面的路由，推荐根据页面配置路由
 |---standalone // 独立的页面，像登录、注册、找回密码是单独的页面。
 |---store  // 项目的全局数据中心，目前包含菜单相关信息、账号相关信息
 |---styles // 组件库的样式
 |---utils  // 一些公共函数库
 |---components.js // 组件库all in one方式，项目中未使用
 |---app.js // 项目的入口文件
 templates  // 一些模板文件
 |---findPwd.html // 本地开发时，找回密码的承载页面，访问路径在build/start.js中配置
 |---index.ejs    // build时候的模板，都共用这一个
 |---login.html   // 本地开发时，登录的承载页面，访问路径在build/start.js中配置
 |---process.html // 首次上线时，商户pc需要有个维护页面，暂停使用之前的功能，该项目上线后该页面没有使用价值
 |---register.html // 本地开发时，注册的承载页面，访问路径在build/start.js中配置
```

# 目录规范
api、routers、pages和components目录都可以以具体页面为维护
