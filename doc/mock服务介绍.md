# mock服务概述
本地开发时支持mock接口到本地，或者mock到RD开发机（线上机器）。
本地mock数据使用[mockjs](http://mockjs.com/)对mock文件进行解析，mock存储在`mock/response`目录中。

# 代理配置
代理相关配置对应的文件夹为 `mock/config`，修改的配置文件都支持热更新，无论是cookie.txt或者是default.config.json：
```
|--cookie.txt  存放cookie请求cookie的地方，因为大多数情况下cookie都是很多，所以单独建立一个文件，方便开发替换
|--default.config.json 默认mock服务的一些配置信息，详情可以参考后面的详细说明
|--default.js  默认转发规则
|--xxxx.js     xxx为git仓库用户名，如果配置了使用git仓库用户名，则会使用该文件
```

## default.config.json
该文件是mock服务的基本配置文件, 支持动态修改该文件，修改之后会重新加载配置文件。

字段说明：
```
{
  "reload": true,  // 是否重新加载mock配置的js文件，如果配置为true则表示修改default.js之类的文件，会触发重新加载，否则不会触发；
  "useGitUser": true, // 是否使用git仓库的用户名作为mock配置的名称，建议启动，这样每一个开发者都可以建自己的mock配置，而不会影响其他人的配置;
  "configFile": "",   // 指定mock配置的名称，如果useGitUser为false而且configFile也为空，就会使用default.js作为mock配置文件
  "cachePath": true,    // 是否缓存已经访问的路径，当配置的proxyList路径较多时，可以避免每次请求都在校验是否需要代理
  "cookieOrder": "browser", // cookie的覆盖规则，如果设置为browser，如果是重复的cookie名称会以浏览器的cookie最高优，否则以本地配置cookie优先
  "printRequestInfo": ["cookie", "method"] 打印请求头的信息，method和query会从request中取，其他的配置从request.headers中取
}
```

ps. 如果设置reload为true, 和 设置cachePath 为true，可能导致更新代理规则之后，还是会使用旧的代理规则，这时候只能重新启动了。

如果不知道git仓库的用户名，可以使用`git config user.name`查看

## default.js或者其他配置文件
字段说明：
```
{
    proxyHost: 'http://mct.y.nuomi.com', // 代理的主机
    proxyList: [   // 需要代理的请求列表
        '/component/captcha/image',
        '/component/captcha/verify',
        '/fang/component/reqsk',
        '/fang/api/accessRecord/saveToPcLogin',
        {
            from: '/fang/api/patchca/**',
            to: 'http://mct.y.nuomi.com',
            headers: {  // 如果此处配置了cookie信息，这块的cookie优先级会比cookie.txt中高优
            }
        }
    ],
    exclude: [ // 不需要代理的路径
        '/dist/*',  // 都需要加上这个，因为静态资源都在dist目录下
    ]
}
```


# mock数据文件
## 文件命名
mock文件路径直接与请求的路径相关联，mock文件首先有个目录概念，目录名称是请求路径的第一层级的路径，其他层级的路径以`_`连接作为文件的名称。
如果只有请求路径只有一层，文件名称也会是这个。
对应关系如下：
```
请求路径                   目录            文件名称

/echo                     echo           echo.js
/echo/test                echo           test.js
/echo/test/home           echo           test_home.js
/demo/test/home           demo           test_home.js
```

## mock文件书写规范
为了方便书写注释，mock都为js文件，文件格式可以是：
```js
module.exports = {
    "code": 0,
    "message" : "成功"
}
```

或者

```
// options 是一个object的里面包含 pathname, query, body三个属性
module.exports = function (options) {
   return {
       code: 0,
       message: 'success'
   }
}
```

返回的数据式可以参考[mockjs示例](http://mockjs.com/examples.html)