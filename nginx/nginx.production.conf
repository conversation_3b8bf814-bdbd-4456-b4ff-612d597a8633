# 查看是否以守护进程的方式运行Nginx 默认是on
daemon off;
# Nginx worker进程个数，根据机器配置有多少cpu配置多少进程即可
worker_processes  8;

error_log /dev/stderr notice;
pid /home/<USER>/nginx/nginx.pid;

events {
    use epoll;
    # 每个work进程最大连接数
    worker_connections  10240;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    resolver *******;
    # 输出日志格式
    log_format main '$http_clientip - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" "$no_bduss_http_cookie" "$http_user_agent" '
                    'rt=$request_time logid=$http_log_id bfe_logid=$http_bfe_logid x_bd_logid=$http_x_bd_logid '
                    '$remote_addr $server_addr $upstream_addr $host '
                    '"$http_x_forwarded_for" ps appEngine - $msec';

    server_names_hash_bucket_size 128;
    client_header_buffer_size 4k;
    large_client_header_buffers 4 32k;
    client_max_body_size 4m;
    client_body_buffer_size 512k;
  
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;

    keepalive_timeout 0;
    chunked_transfer_encoding off;

    underscores_in_headers on;

    gzip off;
    gzip_min_length 1k;
    gzip_buffers 4 16k;
    gzip_http_version 1.0;
    gzip_comp_level 2;
    gzip_types text/plain application/x-javascript text/css application/xml;
    gzip_vary off;

    # proxy_connect_timeout 5s;
    # proxy_read_timeout 5s;
    # proxy_send_timeout 5s;
    # proxy_buffer_size 64k;
    # proxy_buffers 4 64k;
    # proxy_busy_buffers_size 128k;
    # proxy_temp_file_write_size 128k;
    # proxy_set_header Host $http_host; 
    # proxy_set_header Remote-Real-IP $remote_addr;
    # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    # # proxy_http_version 1.1;
    # proxy_intercept_errors on;
    # uninitialized_variable_warn off;

    # limit_req_zone $remote_user zone=req_one:10m rate=1000r/s;
    # limit_conn_zone $remote_user zone=req_two:10m;
  
    server {
        listen 8080;
        server_name_in_redirect off;
        port_in_redirect off;
        absolute_redirect off;
        #server_name  localhost;

        # 访问日志输出目录
        access_log /home/<USER>/nginx/logs/access.log main;
        access_log /dev/stdout main;
        #charset koi8-r;
        #access_log  logs/host.access.log  main;

        add_header Cache-Control no-cache;

        client_body_temp_path /home/<USER>/nginx/client_temp 3 3 4;
        proxy_temp_path /home/<USER>/nginx/proxy_temp 1 2;
        fastcgi_temp_path /home/<USER>/nginx/fastcgi_temp 1 2;
        uwsgi_temp_path /home/<USER>/nginx/uwsgi_temp 1 2;
        scgi_temp_path /home/<USER>/nginx/scgi_temp 1 2;

        # set $baggage_http_header_env "";
        # if ($http_baggage ~* x-mesh-traffic-lane=(.*)){
        #     set $baggage_http_header_env $1;
        # }
        # access_log $baggage_http_header_env;

        # location /mzmis/ {
        #     proxy_cookie_domain muzhi-b-cnap.base-stable.healthsvc.appspace.baidu.com fe-mzmis.base-stable.healthsvc.appspace.baidu.com;   
        #     proxy_pass http://muzhi-b-cnap.base-stable.healthsvc.appspace.baidu.com/mzmis/;
        # }

        # location /mzmis/ {
        #     proxy_cookie_domain https://dev-muzhi.baidu-int.com fe-mzmis.base-stable.healthsvc.appspace.baidu.com;   
        #     proxy_pass  https://dev-muzhi.baidu-int.com/mzmis/;
        # }

        # root 配置为模板文件存放路径
        location / {
            root   /home/<USER>/mzmis-next-fe;
            try_files $uri /index.html;
            index  index.html index.htm;
        }

        location  ~* \.(jpg|jpeg|gif|png|swf|rar|zip|css|js|map|svg|woff|ttf|txt)$ {
            root   /home/<USER>/mzmis-next-fe;
            index index.html;
            rewrite ^/misfe/static/mzmis-fe/(.*)$ /$1 last;
            add_header Access-Control-Allow-Origin *;
        }

        set $no_bduss_http_cookie "";
        if ($http_cookie ~* "(.*)BDUSS=(.+?)(?:;|$)(.*)") {
            set $no_bduss_http_cookie $1$3;
        }
        # redirect server error pages to the static page /50x.html
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
}