module.exports = {
    'errno': 0,
    'errmsg': 'success',
    'data': {
        'max_num': '100',
        'current': [{
            'id': '70',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '100',
            'company_grade': '三级甲等',
            'op_user': 'yangshuai10',
            'create_at': '2021-03-23',
            'status': '1',
        }, {
            'id': '69',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '100',
            'company_grade': '三级乙等',
            'op_user': 'yangshuai10',
            'create_at': '2021-03-23',
            'status': '1',
        }, {
            'id': '68',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '100',
            'company_grade': '三级丙等',
            'op_user': 'yangshuai10',
            'create_at': '2021-03-23',
            'status': '1',
        }, {
            'id': '67',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '100',
            'company_grade': '二级甲等',
            'op_user': 'yangshuai10',
            'create_at': '2021-03-23',
            'status': '1',
        }, {
            'id': '66',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '100',
            'company_grade': '二级乙等',
            'op_user': 'yangshuai10',
            'create_at': '2021-03-23',
            'status': '1',
        }, {
            'id': '65',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '100',
            'company_grade': '二级丙等',
            'op_user': 'yangshuai10',
            'create_at': '2021-03-23',
            'status': '1',
        }, {
            'id': '64',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '100',
            'company_grade': '一级甲等',
            'op_user': 'yangshuai10',
            'create_at': '2021-03-23',
            'status': '1',
        }, {
            'id': '63',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '100',
            'company_grade': '一级乙等',
            'op_user': 'yangshuai10',
            'create_at': '2021-03-23',
            'status': '1',
        }, {
            'id': '62',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '100',
            'company_grade': '一级丙等',
            'op_user': 'yangshuai10',
            'create_at': '2021-03-23',
            'status': '1',
        }, {
            'id': '61',
            'report_price': '3',
            'interflow_price': '7',
            'max_person_price': '100',
            'company_grade': '未知医院',
            'op_user': 'yangshuai10',
            'create_at': '2021-03-23',
            'status': '1',
        }],
        'history': [{
            'id': '70',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '100',
            'company_grade': '三级甲等',
            'op_user': 'yangshuai10',
            'create_at': '2021-03-23',
            'status': '1',
        }, {
            'id': '69',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '100',
            'company_grade': '三级乙等',
            'op_user': 'yangshuai10',
            'create_at': '2021-03-23',
            'status': '1',
        }, {
            'id': '68',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '100',
            'company_grade': '三级丙等',
            'op_user': 'yangshuai10',
            'create_at': '2021-03-23',
            'status': '1',
        }, {
            'id': '67',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '100',
            'company_grade': '二级甲等',
            'op_user': 'yangshuai10',
            'create_at': '2021-03-23',
            'status': '1',
        }, {
            'id': '66',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '100',
            'company_grade': '二级乙等',
            'op_user': 'yangshuai10',
            'create_at': '2021-03-23',
            'status': '1',
        }, {
            'id': '65',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '100',
            'company_grade': '二级丙等',
            'op_user': 'yangshuai10',
            'create_at': '2021-03-23',
            'status': '1',
        }, {
            'id': '64',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '100',
            'company_grade': '一级甲等',
            'op_user': 'yangshuai10',
            'create_at': '2021-03-23',
            'status': '1',
        }, {
            'id': '63',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '100',
            'company_grade': '一级乙等',
            'op_user': 'yangshuai10',
            'create_at': '2021-03-23',
            'status': '1',
        }, {
            'id': '62',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '100',
            'company_grade': '一级丙等',
            'op_user': 'yangshuai10',
            'create_at': '2021-03-23',
            'status': '1',
        }, {
            'id': '61',
            'report_price': '3',
            'interflow_price': '7',
            'max_person_price': '100',
            'company_grade': '未知医院',
            'op_user': 'yangshuai10',
            'create_at': '2021-03-23',
            'status': '1',
        }, {
            'id': '15',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '500',
            'company_grade': '三级甲等',
            'op_user': 'system',
            'create_at': '2021-03-19',
            'status': '2',
        }, {
            'id': '14',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '500',
            'company_grade': '三级乙等',
            'op_user': 'system',
            'create_at': '2021-03-19',
            'status': '2',
        }, {
            'id': '13',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '500',
            'company_grade': '三级丙等',
            'op_user': 'system',
            'create_at': '2021-03-19',
            'status': '2',
        }, {
            'id': '12',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '500',
            'company_grade': '二级甲等',
            'op_user': 'system',
            'create_at': '2021-03-19',
            'status': '2',
        }, {
            'id': '11',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '500',
            'company_grade': '二级乙等',
            'op_user': 'system',
            'create_at': '2021-03-19',
            'status': '2',
        }, {
            'id': '10',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '500',
            'company_grade': '二级丙等',
            'op_user': 'system',
            'create_at': '2021-03-19',
            'status': '2',
        }, {
            'id': '9',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '500',
            'company_grade': '一级甲等',
            'op_user': 'system',
            'create_at': '2021-03-19',
            'status': '2',
        }, {
            'id': '8',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '500',
            'company_grade': '一级乙等',
            'op_user': 'system',
            'create_at': '2021-03-19',
            'status': '2',
        }, {
            'id': '7',
            'report_price': '3',
            'interflow_price': '5',
            'max_person_price': '500',
            'company_grade': '一级丙等',
            'op_user': 'system',
            'create_at': '2021-03-19',
            'status': '2',
        }, {
            'id': '6',
            'report_price': '3',
            'interflow_price': '7',
            'max_person_price': '300',
            'company_grade': '未知医院',
            'op_user': 'system',
            'create_at': '2021-02-07',
            'status': '2',
        }, {
            'id': '5',
            'report_price': '2',
            'interflow_price': '8',
            'max_person_price': '500',
            'company_grade': '未知医院',
            'op_user': 'yangshuai10',
            'create_at': '2021-02-07',
            'status': '2',
        }, {
            'id': '4',
            'report_price': '4',
            'interflow_price': '6',
            'max_person_price': '8',
            'company_grade': '未知医院',
            'op_user': 'yangshuai10',
            'create_at': '2021-02-07',
            'status': '2',
        }, {
            'id': '3',
            'report_price': '1',
            'interflow_price': '7',
            'max_person_price': '10',
            'company_grade': '未知医院',
            'op_user': 'yangshuai10',
            'create_at': '2021-02-07',
            'status': '2',
        }, {
            'id': '2',
            'report_price': '2',
            'interflow_price': '8',
            'max_person_price': '200',
            'company_grade': '未知医院',
            'op_user': 'yangshuai10',
            'create_at': '2021-02-07',
            'status': '2',
        }, {
            'id': '1',
            'report_price': '2',
            'interflow_price': '8',
            'max_person_price': '200',
            'company_grade': '未知医院',
            'op_user': 'yangshuai10',
            'create_at': '2021-02-07',
            'status': '2',
        }],
    },
};