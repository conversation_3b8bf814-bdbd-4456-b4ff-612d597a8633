/**
 * @file 服务质量列表-日
 */

/*eslint-disable*/

module.exports = {
	"errno": 0,
	"errmsg": "success",
	"data": {
		"total": 722066,
		"list": [{
			"ymd": "20191201",
			"ym": "201911",
			"dr_uid": "2574210739",
			"dr_name": "\u4e54\u559c\u5a1f",
			"info_level": "1",
			"dr_level": "1",
			"service_num": "13",
			"inspect_num": "10",
			"inspect_failed_num": "1",
			"inspect_pass_rate": "90",
			"service_seconds": "3080",
			"service_minutes": 51,
			"claim_seconds": "0",
			"avg_claim_seconds": "0"
		}, {
			"ymd": "20191201",
			"dr_uid": "948545448",
			"dr_name": "\u80e1\u96ea",
			"info_level": "1",
			"dr_level": "1",
			"service_num": "10",
			"inspect_num": "9",
			"inspect_failed_num": "1",
			"inspect_pass_rate": "89",
			"service_seconds": "3231",
			"service_minutes": 54,
			"claim_seconds": "0",
			"avg_claim_seconds": "0"
		}, {
			"ymd": "20191201",
			"dr_uid": "1051013556",
			"dr_name": "\u738b\u6960",
			"info_level": "1",
			"dr_level": "1",
			"service_num": "8",
			"inspect_num": "10",
			"inspect_failed_num": "0",
			"inspect_pass_rate": "100",
			"service_seconds": "2177",
			"service_minutes": 36,
			"claim_seconds": "0",
			"avg_claim_seconds": "0"
		}, {
			"ymd": "20191201",
			"dr_uid": "2407400511",
			"dr_name": "\u8881\u51ac",
			"info_level": "1",
			"dr_level": "1",
			"service_num": "9",
			"inspect_num": "3",
			"inspect_failed_num": "0",
			"inspect_pass_rate": "100",
			"service_seconds": "2144",
			"service_minutes": 36,
			"claim_seconds": "0",
			"avg_claim_seconds": "0"
		}, {
			"ymd": "20191201",
			"dr_uid": "323270258",
			"dr_name": "\u90d1\u666f\u8f89",
			"info_level": "1",
			"dr_level": "1",
			"service_num": "11",
			"inspect_num": "10",
			"inspect_failed_num": "0",
			"inspect_pass_rate": "100",
			"service_seconds": "3302",
			"service_minutes": 55,
			"claim_seconds": "0",
			"avg_claim_seconds": "0"
		}, {
			"ymd": "20191201",
			"dr_uid": "1090197244",
			"dr_name": "\u9f9a\u6cc9\u624d",
			"info_level": "1",
			"dr_level": "1",
			"service_num": "6",
			"inspect_num": "10",
			"inspect_failed_num": "0",
			"inspect_pass_rate": "100",
			"service_seconds": "2090",
			"service_minutes": 35,
			"claim_seconds": "0",
			"avg_claim_seconds": "0"
		}, {
			"ymd": "20191201",
			"dr_uid": "185429426",
			"dr_name": "\u674e\u6400",
			"info_level": "1",
			"dr_level": "1",
			"service_num": "11",
			"inspect_num": "10",
			"inspect_failed_num": "0",
			"inspect_pass_rate": "100",
			"service_seconds": "3577",
			"service_minutes": 60,
			"claim_seconds": "0",
			"avg_claim_seconds": "0"
		}, {
			"ymd": "20191201",
			"dr_uid": "785772966",
			"dr_name": "\u738b\u65b0\u660e",
			"info_level": "1",
			"dr_level": "1",
			"service_num": "11",
			"inspect_num": "9",
			"inspect_failed_num": "5",
			"inspect_pass_rate": "44",
			"service_seconds": "3581",
			"service_minutes": 60,
			"claim_seconds": "0",
			"avg_claim_seconds": "0"
		}, {
			"ymd": "20191201",
			"dr_uid": "3264607896",
			"dr_name": "\u4efb\u79c0\u8363",
			"info_level": "1",
			"dr_level": "1",
			"service_num": "8",
			"inspect_num": "10",
			"inspect_failed_num": "0",
			"inspect_pass_rate": "100",
			"service_seconds": "2781",
			"service_minutes": 46,
			"claim_seconds": "0",
			"avg_claim_seconds": "0"
		}, {
			"ymd": "20191201",
			"dr_uid": "2256181078",
			"dr_name": "\u5b8b\u654f",
			"info_level": "1",
			"dr_level": "1",
			"service_num": "13",
			"inspect_num": "9",
			"inspect_failed_num": "0",
			"inspect_pass_rate": "100",
			"service_seconds": "1614",
			"service_minutes": 27,
			"claim_seconds": "0",
			"avg_claim_seconds": "0"
		}]
	}
}