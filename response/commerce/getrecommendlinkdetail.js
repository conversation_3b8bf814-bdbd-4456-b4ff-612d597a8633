// gsk 内容详情
module.exports = {
    'errno': 0,
    'errmsg': 'success',
    'data': {
        'id': 1,
        'title': '主标题',
        'sub_title': '副标题',
        'desc': '主标题+副标题',
        'src': 'm.baidu.com',
        'image_list': '', // 预留字段，不用处理
        'while_type': 2, // 白名单范围；0:不限, 1:uid白名单, 2:自雇
        'while_list': '', // 对应while_type的值，json_string
        'status': 1, // 状态; 0: 下线,1:上线,2:删除
        'create_at': 1625644844,
        'update_at': 1625644844,
    },
    'time': 1234567890,
    'logId': 1234567890,
};
