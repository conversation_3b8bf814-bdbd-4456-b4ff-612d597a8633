module.exports = {
    'errno': 0,
    'errmsg': 'success',
    'data': {
        'list': [
            {
                'id': 1, // 绑定关系id
                'first_channel_id': 1, // 渠道id
                'second_channel_id': 1, // 二级渠道主键id
                'name': 'abb', // 一级渠道名称
                'first_channel': 'abb', // ref
                'second_channel': 'xt61e65d1f', // 二级渠道id
                'second_channel_name': 'show',
                'Invitation_code': '1440226147', // 邀请码
                'picurl': '3967',
                'jump_url': 'https://www.baidu.com',
                'logo_url': 'https://cn.vuejs.org/images/logo.png',
                'update_at': '1610961687',
                'create_at': '1610961687', // 团队成员
            },
            {
                'id': 2, // 绑定关系id
                'first_channel_id': 1, // 渠道id
                'second_channel_id': 1, // 二级渠道主键id
                'name': 'abb', // 一级渠道名称
                'first_channel': '美好的未来就此开始啦', // ref
                'second_channel': 'xt61e65d1f', // 二级渠道id
                'second_channel_name': '任洁芳',
                'Invitation_code': '1440226147', // 邀请码
                'picurl': '3967',
                'jump_url': 'https://www.baidu.com',
                'logo_url': 'https://cn.vuejs.org/images/logo.png',
                'update_at': '1610961687',
                'create_at': '1610961687', // 团队成员
            },
            {
                'id': 3, // 绑定关系id
                'first_channel_id': 1, // 渠道id
                'second_channel_id': 1, // 二级渠道主键id
                'name': '心脏挺玄大会', // 一级渠道名称
                'first_channel': 'abb', // ref
                'second_channel': 'xt61e65d1f', // 二级渠道id
                'second_channel_name': 'show',
                'Invitation_code': '1440226147', // 邀请码
                'picurl': '3967',
                'jump_url': 'https://www.baidu.com',
                'logo_url': 'https://cn.vuejs.org/images/logo.png',
                'update_at': '1610961687',
                'create_at': '1610961687', // 团队成员
            },
        ],
        'total': 42,
    },
};
