module.exports = {
    'errno': 0,
    'errmsg': 'success',
    'data': {
        'list': [{
            'first_channel_id': '1', // 渠道id
            'name': 'abb1', // 渠道名称
            'ref': 'show1', // ref
            'status': 1, // 1开启；0：停用；
            'Invitation_code': '1440226147', // 邀请码
            'picurl': 'https://muzhi-public-pic.cdn.bcebos.com/1854',
            'jump_url': 'https://www.baidu.com',
            'logo_url': 'https://cn.vuejs.org/images/logo.png',
            'update_at': '1610961687',
            'create_at': '1610961687',
        },
        {
            'first_channel_id': '2', // 渠道id
            'name': 'abb2', // 渠道名称
            'ref': 'show2', // ref
            'status': 0, // 1开启；0：停用；
            'Invitation_code': '1440226147', // 邀请码
            'picurl': '3967',
            'jump_url': 'https://www.baidu.com',
            'logo_url': 'https://cn.vuejs.org/images/logo.png',
            'update_at': '1610961687',
            'create_at': '1610961687',
        },
        ],
        'total': 1,
    },
};
