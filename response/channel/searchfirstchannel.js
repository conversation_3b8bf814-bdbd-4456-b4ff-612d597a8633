// 渠道管理 一级渠道名称联系
module.exports = {
    'errno': 0,
    'errmsg': 'success',
    'data': {
        'list': [
            {
                'first_channel_id': '1', // 渠道id
                'name': 'abb', // 渠道名称
                'ref': 'show', // ref
                'status': 1, // 1开启；0：停用；
                'Invitation_code': '1440226147', // 邀请码
                'picurl': '3967',
                'jump_url': 'https://www.baidu.com',
                'logo_url': 'https://cn.vuejs.org/images/logo.png',
                'update_at': '1610961687',
                'create_at': '1610961687', // 团队成员
            },
            {
                'first_channel_id': '2', // 渠道id
                'name': 'add', // 渠道名称
                'ref': 'show', // ref
                'status': 1, // 1开启；0：停用；
                'Invitation_code': '1440226147', // 邀请码
                'picurl': '3967',
                'jump_url': 'https://www.baidu.com',
                'logo_url': 'https://cn.vuejs.org/images/logo.png',
                'update_at': '1610961687',
                'create_at': '1610961687', // 团队成员
            },
        ],
        'total': 1,
    },
};
