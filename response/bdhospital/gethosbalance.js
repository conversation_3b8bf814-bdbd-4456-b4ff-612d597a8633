module.exports = {
    'errno': 0,
    'status': 0,
    'errmsg': 'success',
    'msg': 'success',
    'logId': 2231059211,
    'data': {
        'list': [
            {
                'id': '61',
                'organization_id': '3019',
                'organization_name': '测试民营医院',
                'storage': '991',
                'info': '',
                'is_del': '1',
                'status': '0',
                'version': '0',
                'create_at': '1711954869',
                'update_at': '1733129993',
            },
            {
                'id': '55',
                'organization_id': '18453',
                'organization_name': '王韩测试企业',
                'storage': '684',
                'info': '',
                'is_del': '1',
                'status': '0',
                'version': '0',
                'create_at': '1709884696',
                'update_at': '1732693189',
            },
            {
                'id': '53',
                'organization_id': '18445',
                'organization_name': '肖赞企业',
                'storage': '97',
                'info': '',
                'is_del': '1',
                'status': '0',
                'version': '0',
                'create_at': '1709884240',
                'update_at': '1710824594',
            },
            {
                'id': '54',
                'organization_id': '18447',
                'organization_name': '羡晓静的测试企业',
                'storage': '99',
                'info': '',
                'is_del': '1',
                'status': '0',
                'version': '0',
                'create_at': '1709884531',
                'update_at': '1709888313',
            },
            {
                'id': '58',
                'organization_id': '18448',
                'organization_name': 'wqewqewqe@###!!#~~~~',
                'storage': '1220',
                'info': '',
                'is_del': '1',
                'status': '0',
                'version': '0',
                'create_at': '1709885070',
                'update_at': '1709888057',
            },
            {
                'id': '59',
                'organization_id': '18452',
                'organization_name': 'test by ren',
                'storage': '109',
                'info': '',
                'is_del': '1',
                'status': '0',
                'version': '0',
                'create_at': '1709885402',
                'update_at': '1709887806',
            },
            {
                'id': '56',
                'organization_id': '18451',
                'organization_name': 'QA测试医院',
                'storage': '9',
                'info': '',
                'is_del': '1',
                'status': '0',
                'version': '0',
                'create_at': '1709884775',
                'update_at': '1709886764',
            },
            {
                'id': '60',
                'organization_id': '18455',
                'organization_name': '于潇川注册企业名称-0308测试',
                'storage': '123',
                'info': '',
                'is_del': '1',
                'status': '0',
                'version': '0',
                'create_at': '1709885700',
                'update_at': '1709885700',
            },
            {
                'id': '57',
                'organization_id': '18450',
                'organization_name': '随意--240308-生成',
                'storage': '400',
                'info': '',
                'is_del': '1',
                'status': '0',
                'version': '0',
                'create_at': '1709884867',
                'update_at': '1709884867',
            },
            {
                'id': '37',
                'organization_id': '18410',
                'organization_name': '北京大学人民医院',
                'storage': '10',
                'info': '',
                'is_del': '1',
                'status': '0',
                'version': '0',
                'create_at': '1669086334',
                'update_at': '1709792362',
            },
            {
                'id': '51',
                'organization_id': '10026',
                'organization_name': '羡晓静测试用',
                'storage': '98',
                'info': '',
                'is_del': '1',
                'status': '0',
                'version': '0',
                'create_at': '1707207344',
                'update_at': '1708937545',
            },
            {
                'id': '52',
                'organization_id': '10031',
                'organization_name': '北京市丰台区北宫镇社区卫生服务中心',
                'storage': '993',
                'info': '',
                'is_del': '1',
                'status': '0',
                'version': '0',
                'create_at': '1707291849',
                'update_at': '1708417455',
            },
            {
                'id': '50',
                'organization_id': '10020',
                'organization_name': '北京大学人民医院',
                'storage': '78',
                'info': '',
                'is_del': '1',
                'status': '0',
                'version': '0',
                'create_at': '1706765761',
                'update_at': '1707101582',
            },
            {
                'id': '43',
                'organization_id': '18440',
                'organization_name': '测试医院名称test',
                'storage': '36',
                'info': '',
                'is_del': '1',
                'status': '0',
                'version': '0',
                'create_at': '1679295956',
                'update_at': '1706788361',
            },
            {
                'id': '49',
                'organization_id': '10027',
                'organization_name': '北京协和医院',
                'storage': '93',
                'info': '',
                'is_del': '1',
                'status': '0',
                'version': '0',
                'create_at': '1706759578',
                'update_at': '1706763519',
            },
            {
                'id': '48',
                'organization_id': '10029',
                'organization_name': '于潇川测试1220',
                'storage': '1',
                'info': '',
                'is_del': '1',
                'status': '0',
                'version': '0',
                'create_at': '1706699645',
                'update_at': '1706699645',
            },
            {
                'id': '47',
                'organization_id': '18437',
                'organization_name': '上海市第一人民医院',
                'storage': '0',
                'info': '',
                'is_del': '1',
                'status': '0',
                'version': '0',
                'create_at': '1669086334',
                'update_at': '1687328844',
            },
            {
                'id': '42',
                'organization_id': '18421',
                'organization_name': '123dasdsadas',
                'storage': '4407',
                'info': '',
                'is_del': '1',
                'status': '0',
                'version': '0',
                'create_at': '1679036797',
                'update_at': '1686194159',
            },
            {
                'id': '46',
                'organization_id': '18429',
                'organization_name': '新医院入驻改集团2-分院1',
                'storage': '249',
                'info': '',
                'is_del': '1',
                'status': '0',
                'version': '0',
                'create_at': '1686049928',
                'update_at': '1686051309',
            },
            {
                'id': '44',
                'organization_id': '18406',
                'organization_name': '健康1',
                'storage': '60',
                'info': '',
                'is_del': '1',
                'status': '0',
                'version': '0',
                'create_at': '1686037950',
                'update_at': '1686037951',
            },
        ],
        'total': 30,
    },
};
