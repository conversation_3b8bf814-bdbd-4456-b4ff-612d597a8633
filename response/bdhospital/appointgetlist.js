/*
 * @Descripttion: descripttion
 * @Author: <EMAIL>
 * @Date: 2021-06-09 14:18:43
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2021-06-09 14:18:44
 */
module.exports = {
    "errno": 0,
    "errmsg": "success",
    "data": {
        "list": [
            {
                "id": "1",
                "treat_date": "2021-06-07",
                "time_segment": 1,
                "treate_address": "出诊地点",
                "uid": "123123123",
                "appoint_id": "c595b838d3b830181c6789b90e5d0659",
                "hospital_id": "28",
                "cid_name": "科室",
                "hospital_name": "上海中医药大学附属曙光医院西院",
                "doc_name": "医生名称",
                "boverdue": 1,
                "appoint_status": 1,
                "sell_num": 1,
                "appoint_num": "10",
                "treat_type": "1",
                "first_visit_price": "3000",
                "subsequent_visit_price": "2599",
                "type": "1",
                "status": "1",
                "create_at": "1604560181",
                "update_at": "1604560181"
            }
        ],
        "count": 1,
        "config": {
            "appoint_status_mapping": {
                "1": "正常",
                "2": "停诊",
                "3": "约满"
            },
            "b_overdue_mapping": {
                "1": "已过期",
                "2": "未过期"
            },
            "treat_type_mapping": {
                "1": "普通",
                "2": "专家"
            },
            "appoint_time_segment_map": {
                "1": "上午",
                "2": "下午",
                "3": "晚上"
            }
        }
    }
}