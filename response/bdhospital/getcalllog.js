// 多点医院 订单 通话记录
module.exports = {
    "errno": 0,
    "errmsg": "success",
    "data": [
      {
        "order_id": "svc_8518_1615794483978596", // 订单ID
        "hospital_name": "测试医院", // 医院名称
        "patient_name": "测试患者名称", // 患者名称
        "call_phone": 18976897890, // 联系手机号
        "call_status": 1, // 呼叫状态：1 成功 2失败
        "call_time": "2021-03-25 11:30:00", // 拨打时间
        "talk_time": 65, // 拨打时长（秒）
        "sound_record": "http://bj.bcebos.com/v1/cp-privacy/2/2021/03/25/92021032516093509901545860_95092120_13962298337_20210325160935_BD70.wav?authorization=bce-auth-v1%2F61bf983af7ca4d7fa6412ed517c63e3e%2F2021-03-25T08%3A15%3A57Z%2F-1%2F%2F6e3426ad0790ec5f88424bc517bf028de72f6cca885687b14a4ea4c24ecb8652"
      },
      {
        "order_id": "svc_8518_1615794483978596",
        "hospital_name": "测试医院",
        "patient_name": "测试患者名称",
        "call_phone": 15676897890,
        "call_status": 1,
        "call_time": "2021-03-24 11:30:00",
        "talk_time": 75,
        "sound_record": ""
      },
      {
        "order_id": "svc_8518_1615794483978596", // 订单ID
        "hospital_name": "测试医院", // 医院名称
        "patient_name": "测试患者名称", // 患者名称
        "call_phone": 18976897890, // 联系手机号
        "call_status": 1, // 呼叫状态：1 成功 2失败
        "call_time": "2021-03-25 11:30:00", // 拨打时间
        "talk_time": 65, // 拨打时长（秒）
        "sound_record": "http://bj.bcebos.com/v1/cp-privacy/2/2021/03/25/92021032516093509901545860_95092120_13962298337_20210325160935_BD70.wav?authorization=bce-auth-v1%2F61bf983af7ca4d7fa6412ed517c63e3e%2F2021-03-25T08%3A15%3A57Z%2F-1%2F%2F6e3426ad0790ec5f88424bc517bf028de72f6cca885687b14a4ea4c24ecb8652"
      },
      {
        "order_id": "svc_8518_1615794483978596",
        "hospital_name": "测试医院",
        "patient_name": "测试患者名称",
        "call_phone": 15676897890,
        "call_status": 1,
        "call_time": "2021-03-24 11:30:00",
        "talk_time": 75,
        "sound_record": ""
      },
      {
        "order_id": "svc_8518_1615794483978596", // 订单ID
        "hospital_name": "测试医院", // 医院名称
        "patient_name": "测试患者名称", // 患者名称
        "call_phone": 18976897890, // 联系手机号
        "call_status": 1, // 呼叫状态：1 成功 2失败
        "call_time": "2021-03-25 11:30:00", // 拨打时间
        "talk_time": 65, // 拨打时长（秒）
        "sound_record": "http://bj.bcebos.com/v1/cp-privacy/2/2021/03/25/92021032516093509901545860_95092120_13962298337_20210325160935_BD70.wav?authorization=bce-auth-v1%2F61bf983af7ca4d7fa6412ed517c63e3e%2F2021-03-25T08%3A15%3A57Z%2F-1%2F%2F6e3426ad0790ec5f88424bc517bf028de72f6cca885687b14a4ea4c24ecb8652"
      },
      {
        "order_id": "svc_8518_1615794483978596",
        "hospital_name": "测试医院",
        "patient_name": "测试患者名称",
        "call_phone": 15676897890,
        "call_status": 1,
        "call_time": "2021-03-24 11:30:00",
        "talk_time": 75,
        "sound_record": ""
      },
      {
        "order_id": "svc_8518_1615794483978596", // 订单ID
        "hospital_name": "测试医院", // 医院名称
        "patient_name": "测试患者名称", // 患者名称
        "call_phone": 18976897890, // 联系手机号
        "call_status": 1, // 呼叫状态：1 成功 2失败
        "call_time": "2021-03-25 11:30:00", // 拨打时间
        "talk_time": 65, // 拨打时长（秒）
        "sound_record": "http://bj.bcebos.com/v1/cp-privacy/2/2021/03/25/92021032516093509901545860_95092120_13962298337_20210325160935_BD70.wav?authorization=bce-auth-v1%2F61bf983af7ca4d7fa6412ed517c63e3e%2F2021-03-25T08%3A15%3A57Z%2F-1%2F%2F6e3426ad0790ec5f88424bc517bf028de72f6cca885687b14a4ea4c24ecb8652"
      },
      {
        "order_id": "svc_8518_1615794483978596",
        "hospital_name": "测试医院",
        "patient_name": "测试患者名称",
        "call_phone": 15676897890,
        "call_status": 1,
        "call_time": "2021-03-24 11:30:00",
        "talk_time": 75,
        "sound_record": ""
      },
      {
        "order_id": "svc_8518_1615794483978596", // 订单ID
        "hospital_name": "测试医院", // 医院名称
        "patient_name": "测试患者名称", // 患者名称
        "call_phone": 18976897890, // 联系手机号
        "call_status": 1, // 呼叫状态：1 成功 2失败
        "call_time": "2021-03-25 11:30:00", // 拨打时间
        "talk_time": 65, // 拨打时长（秒）
        "sound_record": "http://bj.bcebos.com/v1/cp-privacy/2/2021/03/25/92021032516093509901545860_95092120_13962298337_20210325160935_BD70.wav?authorization=bce-auth-v1%2F61bf983af7ca4d7fa6412ed517c63e3e%2F2021-03-25T08%3A15%3A57Z%2F-1%2F%2F6e3426ad0790ec5f88424bc517bf028de72f6cca885687b14a4ea4c24ecb8652"
      },
      {
        "order_id": "svc_8518_1615794483978596",
        "hospital_name": "测试医院",
        "patient_name": "测试患者名称",
        "call_phone": 15676897890,
        "call_status": 1,
        "call_time": "2021-03-24 11:30:00",
        "talk_time": 75,
        "sound_record": ""
      }
    ]
  }
