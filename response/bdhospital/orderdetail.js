module.exports = {
    'errno': 0,
    'errmsg': 'success',
    'data': {
        'gh_info': {
            'treat_date': '2023-10-21',
            'time_segment': '2',
            'price': '11.00',
            'hospital': '\u6e05\u534e\u533b\u9662',
            'address': '北苑家园',
            'phone': '400-100-200',
            'outpatient_type': '1',
            'patient_disease': '\u8111\u888b\u75bc',
            'cid_text': '\u6d4b\u8bd5\u79d1\u5ba41',
            'doctor_name': '\u5f90\u6d77\u6d9b',
            'complaint': '\u8111\u888b\u75bc',
        },
        'base_info': {
            'order_id': 'svc_1638269397758010518',
            'patient_name': '\u6881\u7428\u6d4b\u8bd5',
            'bd_patient_id': 'ct_1628757845743155',
            'sex': '\u7537',
            'age': '0',
            'province': '\u5317\u4eac',
            'city': '\u6d77\u6dc0\u533a',
            'contact': 'DzWEmlPKHuzDOaeWs+RCDg==',
            'doctor_uid': '645827848',
            'status': '1',
            'external_status': '1',
            'reject_at': '',
            'reject_reasion': '',
            'reject_operator': '',
            'check_reason': '',
            'work_order_status': 0,
            'origin_info': {
                'origin_treat_date': '2023-10-21', // 原始时间
                'origin_time_segment': '3',
                'origin_address': '北苑家园2', // 原始地址
            },
        },
        'config': {
            'status_config': {
                '1': '\u5f85\u4ed8\u6b3e',
                '2': '\u60a3\u8005\u672a\u652f\u4ed8\u53d6\u6d88',
                '3': '\u8d85\u65f6\u53d6\u6d88',
                '4': '\u5df2\u4ed8\u6b3e',
                '5': '\u9000\u6b3e\u4e2d',
                '6': '\u5df2\u9000\u6b3e',
            },
            'c_status_config': {
                '1': '\u5f85\u652f\u4ed8',
                '2': '\u652f\u4ed8\u524d\u7528\u6237\u53d6\u6d88\uff1b',
                '3': '\u652f\u4ed8\u524d\u8d85\u65f6\u53d6\u6d88\uff1b',
                '4': '\u5df2\u652f\u4ed8\uff1b',
                '5': 'TP\u65b9\u7533\u8bf7\u8865\u9000\u56de\u8c03\u901a\u77e5\uff0c\u9000\u6b3e\u4e2d',
                '6': 'TP\u65b9\u7533\u8bf7\u8865\u9000\u56de\u8c03\u901a\u77e5\uff0c\u9000\u6b3e\u5b8c\u6210',
                '7': '\u652f\u4ed8\u540e\u7528\u6237\u53d6\u6d88\u4e0d\u9000\u6b3e',
                '8': '\u652f\u4ed8\u540e\u7528\u6237\u53d6\u6d88\u5e76\u9000\u6b3e\uff1b',
                '9': '\u8d85\u8fc7\u5c31\u8bca\u65f6\u95f4\u81ea\u52a8\u6838\u9500\u5b8c\u6210\uff1b',
                '10': '\u6838\u9500\u540e\u5df2\u8bc4\u4ef7\uff1b',
                '11': '\u652f\u4ed8\u540e\u7cfb\u7edf\u53d6\u6d88\u4e0d\u9000\u6b3e',
                '12': '\u652f\u4ed8\u540e\u7cfb\u7edf\u53d6\u6d88\u5e76\u9000\u6b3e',
                '13': '\u9000\u6b3e\u4e2d\uff1b',
                '14': '\u9000\u6b3e\u5b8c\u6210\uff0c\u6216\u8865\u9000\u5b8c\u6210\uff1b',
                '15': 'TP\u65b9\u8981\u6c42\u9000\u6b3e',
                '16': 'TP\u9000\u6b3e\u4e2d\uff0cc\u7aef\u5df2\u7ecf\u5728\u8d70\u9000\u6b3e\u6d41\u7a0b',
                '17': '\u8d85\u65f6\u53d6\u6d88',
            },
            'time_segment_config': {
                '1': '\u4e0a\u5348',
                '2': '\u4e0b\u5348',
                '3': '\u665a\u4e0a',
            },
            'outpatient_type_config': {
                '1': '\u4e13\u5bb6\u95e8\u8bca',
                '2': '\u666e\u901a\u95e8\u8bca',
            },
            'patient_type_config': ['\u521d\u8bca', '\u590d\u8bca'],
            'mcn_list_config': {
                '21': '\u6709\u6765asdf',
                '20': '',
                '19': '\u6d4b\u8bd5\u4fee\u6539\u4e86\u4e00\u4e2a',
                '18': '\u6d4b\u8bd5\u65b0\u589e',
                '17': '1234567',
                '16': '\u674e\u4f73\u822a\u4fee123434',
                '15': '22344',
                '14': '11',
                '13': '\u745e\u5178\u56fd\u9632\u548c\u5065\u5eb7',
                '12': '123453321312',
                '11': '1234. ',
                '10': '123213333',
                '9': '\u7b2c\u516d\u6b21\u6dfb\u52a0eqw',
                '8': '\u6d4b\u8bd5\u6dfb\u52a0',
                '7': 'dsfghjkl',
                '6': '123',
                '5': '\u6709\u6765',
                '4': '\u7b2c\u4e09\u6b21\u6dfb\u52a0',
                '3': 'okokok',
                '2': '\u7b2c\u4e00\u6b21\u4fee\u6539',
                '1': '\u5457\u90fd\u4e0d1',
            },
            'face_diagnosis_status_map': [
                '\u65e0',
                '\u5df2\u62e8\u6253',
                '\u9700\u518d\u6b21\u62e8\u6253',
                '\u5df2\u53d1\u9001\u77ed\u4fe1',
                '\u723d\u7ea6\u672a\u5c31\u8bca',
                '\u723d\u7ea6\u672a\u5c31\u8bca',
            ],
        },
    },
};
