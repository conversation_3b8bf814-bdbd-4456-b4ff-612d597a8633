module.exports =
{
  "errno": 0,
  "errmsg": "success",
  "data": {
    "list": [
      {
        "id": "32",
        "uid": "2500574176",
        "hospital_id": "18410",
        "hospital_name": "联调测试医院",
        "name": "qatest9",
        "time_segment_info": [
          {
            "treat_date": "2020-11-24",
            "time_segment": "3"
          }
        ],
        "appoint_num": "11",
        "treat_type": "2",
        "first_visit_price": 0.01,
        "subsequent_visit_price": 0.01,
        "section": "联调测试科室2222",
        "type": "1",
        "status": "2",
        "operator": "yangshuai10",
        "reason": "",
        "create_at": "1606117617",
        "update_at": "1606117625",
        "date_info": {
          "2020-11-24": [
            "3"
          ]
        }
      },
      {
        "id": "30",
        "uid": "2500574176",
        "hospital_id": "18410",
        "hospital_name": "联调测试医院",
        "name": "qatest9",
        "time_segment_info": [
          {
            "treat_date": "2020-11-24",
            "time_segment": "1"
          },
          {
            "treat_date": "2020-11-24",
            "time_segment": "2"
          },
          {
            "treat_date": "2020-11-25",
            "time_segment": "3"
          },
          {
            "treat_date": "2020-11-25",
            "time_segment": "2"
          }
        ],
        "appoint_num": "10",
        "treat_type": "1",
        "first_visit_price": 0.01,
        "subsequent_visit_price": 0.01,
        "section": "联调测试科室2222",
        "type": "1",
        "status": "2",
        "operator": "yangshuai10",
        "reason": "",
        "create_at": "1606109794",
        "update_at": "1606109809",
        "date_info": {
          "2020-11-24": [
            "1",
            "2"
          ],
          "2020-11-25": [
            "2",
            "3"
          ]
        }
      },
      {
        "id": "29",
        "uid": "2500318629",
        "hospital_id": "18410",
        "hospital_name": "联调测试医院",
        "name": "2500318629",
        "time_segment_info": [
          {
            "treat_date": "2020-11-21",
            "time_segment": "3"
          },
          {
            "treat_date": "2020-11-22",
            "time_segment": "3"
          }
        ],
        "appoint_num": "20",
        "treat_type": "2",
        "first_visit_price": 0.01,
        "subsequent_visit_price": 0.01,
        "section": "联调测试科室",
        "type": "1",
        "status": "2",
        "operator": "yangshuai10",
        "reason": "",
        "create_at": "1605765972",
        "update_at": "1605765995",
        "date_info": {
          "2020-11-21": [
            "3"
          ],
          "2020-11-22": [
            "3"
          ]
        }
      },
      {
        "id": "28",
        "uid": "2500318629",
        "hospital_id": "18410",
        "hospital_name": "联调测试医院",
        "name": "2500318629",
        "time_segment_info": [
          {
            "treat_date": "2020-11-18",
            "time_segment": "2"
          },
          {
            "treat_date": "2020-11-20",
            "time_segment": "2"
          }
        ],
        "appoint_num": "11",
        "treat_type": "2",
        "first_visit_price": 11,
        "subsequent_visit_price": 11,
        "section": "联调测试科室",
        "type": "1",
        "status": "2",
        "operator": "yangshuai10",
        "reason": "",
        "create_at": "1605691911",
        "update_at": "1605762383",
        "date_info": {
          "2020-11-18": [
            "2"
          ],
          "2020-11-20": [
            "2"
          ]
        }
      },
      {
        "id": "27",
        "uid": "2500318629",
        "hospital_id": "18410",
        "hospital_name": "联调测试医院",
        "name": "2500318629",
        "time_segment_info": [
          {
            "treat_date": "2020-11-18",
            "time_segment": "1"
          },
          {
            "treat_date": "2020-11-20",
            "time_segment": "2"
          },
          {
            "treat_date": "2020-12-04",
            "time_segment": "3"
          },
          {
            "treat_date": "2020-12-25",
            "time_segment": "3"
          }
        ],
        "appoint_num": "11",
        "treat_type": "1",
        "first_visit_price": 11,
        "subsequent_visit_price": 11,
        "section": "联调测试科室",
        "type": "1",
        "status": "3",
        "operator": "yangshuai10",
        "reason": "123123123",
        "create_at": "1605601709",
        "update_at": "1605691324",
        "date_info": {
          "2020-11-18": [
            "1"
          ],
          "2020-11-20": [
            "2"
          ],
          "2020-12-04": [
            "3"
          ],
          "2020-12-25": [
            "3"
          ]
        }
      },
      {
        "id": "25",
        "uid": "2500318629",
        "hospital_id": "18410",
        "hospital_name": "联调测试医院",
        "name": "2500318629",
        "time_segment_info": [
          {
            "treat_date": "2020-11-18",
            "time_segment": "3"
          },
          {
            "treat_date": "2020-11-20",
            "time_segment": "3"
          }
        ],
        "appoint_num": "10",
        "treat_type": "1",
        "first_visit_price": 15,
        "subsequent_visit_price": 10,
        "section": "联调测试科室",
        "type": "1",
        "status": "2",
        "operator": "yangshuai10",
        "reason": "",
        "create_at": "1605582274",
        "update_at": "1605582325",
        "date_info": {
          "2020-11-18": [
            "3"
          ],
          "2020-11-20": [
            "3"
          ]
        }
      }
    ],
    "total": 6,
    "pn": 2,
    "has_more": 0,
    "config": {
      "status_config": {
        "1": "待审核",
        "2": "通过",
        "3": "拒绝"
      },
      "type_config": {
        "1": "出诊",
        "2": "停诊"
      },
      "treat_type_config": {
        "1": "普通",
        "2": "专家"
      },
      "time_segment_config": {
        "1": "上午",
        "2": "下午",
        "3": "晚上"
      }
    }
  }
}
