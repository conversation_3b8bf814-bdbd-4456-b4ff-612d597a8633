module.exports = {
    "errno": 0,
    "errmsg": "success",
    "data": {
        "list": [
            {
                "id": "1", //申诉id
                "order_id": "2", //订单ID
                "type": "1", //申诉类型
                "description": "医生不在现场", //申诉详情
                "expectation": "退款", //期望处理方案
                "reply": "你好啊真的好吗发发呆发呆发呆发水淀粉", //回复内容
                "phone": "1880101010", //联系方式
                "status": "1" //处理状态
            },
            {
                "id": "2", //申诉id
                "order_id": "3", //订单ID
                "type": "2", //申诉类型
                "description": "医生不在现场", //申诉详情
                "expectation": "退款", //期望处理方案
                "reply": "你好啊真的好吗发发呆发呆发呆发水淀粉,朝闻道，夕死可矣。朝闻道，夕死可矣朝闻道，夕死可矣朝闻道，夕死可矣朝闻道，夕死可矣", //回复内容
                "phone": "1880101010", //联系方式
                "status": "2" //处理状态
            }
        ],
        "total": 22,
        "config": {
            "status_config": {
                "1": "待处理",
                "2": "已处理"
            },
            "type_config": {
                "1": "申请退款",
                "2": "退款未收到",
                "3": "投诉医院",
                "4": "投诉医生",
                "5": "其他问题"
            }
        }
    }
}
