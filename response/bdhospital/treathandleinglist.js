module.exports = {
    "errno": 0,
    "errmsg": "success",
    "data": {
        "list": [{
            "bd_order_id": "svc_1019_1606225249485605",
            "patient_name": "test2",
            "doctor_name": "\u9648\u56fd\u826f",
            "hospital_name": "\u6e05\u534e\u533b\u9662",
            "cid_name": "SHOWCASE",
            "create_at": "2020-11-24 21:40:49",
            "treat_date": "2020-11-28",
            "time_segment": "1",
            "external_status": "4",
            "status": "4",
            "price": "0.02",
            "outpatient_type": "1",
            "uid": "**********",
            "is_can_refund": "1",
            "is_urgent": true
        }, {
            "bd_order_id": "svc_1019_1606271446689162",
            "patient_name": "\u5f20\u4e09",
            "doctor_name": "\u9648\u56fd\u826f",
            "hospital_name": "\u6e05\u534e\u533b\u9662",
            "cid_name": "SHOWCASE",
            "create_at": "2020-11-25 10:30:46",
            "treat_date": "2020-11-30",
            "time_segment": "1",
            "external_status": "4",
            "status": "4",
            "price": "0.01",
            "outpatient_type": "2",
            "uid": "**********",
            "is_can_refund": "1",
            "is_urgent": true
        }, {
            "bd_order_id": "svc_1019_1606271784140334",
            "patient_name": "\u7b49\u7b49",
            "doctor_name": "\u9648\u56fd\u826f",
            "hospital_name": "\u6e05\u534e\u533b\u9662",
            "cid_name": "SHOWCASE",
            "create_at": "2020-11-25 10:36:25",
            "treat_date": "2020-11-30",
            "time_segment": "1",
            "external_status": "4",
            "status": "4",
            "price": "0.01",
            "outpatient_type": "2",
            "uid": "**********",
            "is_can_refund": "1",
            "is_urgent": true
        }, {
            "bd_order_id": "svc_1019_1606271948273065",
            "patient_name": "\u5927\u854a\u854a",
            "doctor_name": "\u9648\u56fd\u826f",
            "hospital_name": "\u6e05\u534e\u533b\u9662",
            "cid_name": "SHOWCASE",
            "create_at": "2020-11-25 10:39:09",
            "treat_date": "2020-11-30",
            "time_segment": "1",
            "external_status": "4",
            "status": "4",
            "price": "0.01",
            "outpatient_type": "2",
            "uid": "**********",
            "is_can_refund": "1",
            "is_urgent": true
        }, {
            "bd_order_id": "svc_1019_1606467205664117",
            "patient_name": "asd18",
            "doctor_name": "\u9648\u56fd\u826f",
            "hospital_name": "\u6e05\u534e\u533b\u9662",
            "cid_name": "SHOWCASE",
            "create_at": "2020-11-27 16:53:25",
            "treat_date": "2020-12-01",
            "time_segment": "3",
            "external_status": "14",
            "status": "6",
            "price": "0.02",
            "outpatient_type": "1",
            "uid": "**********",
            "is_can_refund": "0",
            "is_urgent": true
        }, {
            "bd_order_id": "svc_1019_1606464404438061",
            "patient_name": "asd22",
            "doctor_name": "\u9648\u56fd\u826f",
            "hospital_name": "\u6e05\u534e\u533b\u9662",
            "cid_name": "SHOWCASE",
            "create_at": "2020-11-27 16:06:44",
            "treat_date": "2020-12-03",
            "time_segment": "1",
            "external_status": "4",
            "status": "4",
            "price": "0.02",
            "outpatient_type": "1",
            "uid": "**********",
            "is_can_refund": "1",
            "is_urgent": true
        }, {
            "bd_order_id": "svc_1019_1606466722282001",
            "patient_name": "asda17",
            "doctor_name": "\u9648\u56fd\u826f",
            "hospital_name": "\u6e05\u534e\u533b\u9662",
            "cid_name": "SHOWCASE",
            "create_at": "2020-11-27 16:45:22",
            "treat_date": "2020-12-03",
            "time_segment": "3",
            "external_status": "4",
            "status": "4",
            "price": "0.02",
            "outpatient_type": "1",
            "uid": "**********",
            "is_can_refund": "1",
            "is_urgent": true
        }, {
            "bd_order_id": "svc_1019_1606314203935500",
            "patient_name": "asd",
            "doctor_name": "\u9648\u56fd\u826f",
            "hospital_name": "\u6e05\u534e\u533b\u9662",
            "cid_name": "SHOWCASE",
            "create_at": "2020-11-25 22:23:24",
            "treat_date": "2020-12-04",
            "time_segment": "1",
            "external_status": "4",
            "status": "4",
            "price": "0.02",
            "outpatient_type": "2",
            "uid": "**********",
            "is_can_refund": "1",
            "is_urgent": true
        }, {
            "bd_order_id": "svc_1019_1606314408891694",
            "patient_name": "32312",
            "doctor_name": "\u9648\u56fd\u826f",
            "hospital_name": "\u6e05\u534e\u533b\u9662",
            "cid_name": "SHOWCASE",
            "create_at": "2020-11-25 22:26:48",
            "treat_date": "2020-12-04",
            "time_segment": "1",
            "external_status": "4",
            "status": "4",
            "price": "0.02",
            "outpatient_type": "2",
            "uid": "**********",
            "is_can_refund": "1",
            "is_urgent": true
        }, {
            "bd_order_id": "svc_1019_1606315636868145",
            "patient_name": "asd18",
            "doctor_name": "\u9648\u56fd\u826f",
            "hospital_name": "\u6e05\u534e\u533b\u9662",
            "cid_name": "SHOWCASE",
            "create_at": "2020-11-25 22:47:17",
            "treat_date": "2020-12-04",
            "time_segment": "1",
            "external_status": "4",
            "status": "4",
            "price": "0.02",
            "outpatient_type": "2",
            "uid": "**********",
            "is_can_refund": "1",
            "is_urgent": true
        }, {
            "bd_order_id": "svc_1019_1606465154985216",
            "patient_name": "asd19",
            "doctor_name": "\u9648\u56fd\u826f",
            "hospital_name": "\u6e05\u534e\u533b\u9662",
            "cid_name": "SHOWCASE",
            "create_at": "2020-11-27 16:19:15",
            "treat_date": "2020-12-04",
            "time_segment": "2",
            "external_status": "4",
            "status": "4",
            "price": "0.02",
            "outpatient_type": "2",
            "uid": "**********",
            "is_can_refund": "1",
            "is_urgent": true
        }, {
            "bd_order_id": "svc_1019_1606465290482758",
            "patient_name": "asd22",
            "doctor_name": "\u9648\u56fd\u826f",
            "hospital_name": "\u6e05\u534e\u533b\u9662",
            "cid_name": "SHOWCASE",
            "create_at": "2020-11-27 16:21:31",
            "treat_date": "2020-12-04",
            "time_segment": "3",
            "external_status": "4",
            "status": "4",
            "price": "0.02",
            "outpatient_type": "2",
            "uid": "**********",
            "is_can_refund": "1",
            "is_urgent": true
        }, {
            "bd_order_id": "svc_1019_1606467019528645",
            "patient_name": "asd19",
            "doctor_name": "\u9648\u56fd\u826f",
            "hospital_name": "\u6e05\u534e\u533b\u9662",
            "cid_name": "SHOWCASE",
            "create_at": "2020-11-27 16:50:19",
            "treat_date": "2021-01-15",
            "time_segment": "3",
            "external_status": "15",
            "status": "5",
            "price": "0.02",
            "outpatient_type": "2",
            "uid": "**********",
            "is_can_refund": "0",
            "is_urgent": true
        }, {
            "bd_order_id": "yuiophjklbnjkffffdffaff93090",
            "patient_name": "\u5f20\u4e09",
            "doctor_name": "\u9648\u56fd\u826f",
            "hospital_name": "\u6e05\u534e\u533b\u9662",
            "cid_name": "SHOWCASE",
            "create_at": "2021-02-18 16:57:33",
            "treat_date": "2021-01-25",
            "time_segment": "2",
            "external_status": "15",
            "status": "5",
            "price": "78.00",
            "outpatient_type": "2",
            "uid": "**********",
            "is_can_refund": "0",
            "is_urgent": true
        }, {
            "bd_order_id": "svc_1615700589168755",
            "patient_name": "\u9a6c\u6668\u9a70",
            "doctor_name": "\u9648\u56fd\u826f",
            "hospital_name": "\u6e05\u534e\u533b\u9662",
            "cid_name": "\u6d4b\u8bd5",
            "create_at": "2021-03-14 13:43:10",
            "treat_date": "2021-03-15",
            "time_segment": "2",
            "external_status": "1",
            "status": "1",
            "price": "0.01",
            "outpatient_type": "1",
            "uid": "**********",
            "is_can_refund": "0",
            "is_urgent": true
        }, {
            "bd_order_id": "svc_8518_1615705354643802",
            "patient_name": "\u9a6c\u6668\u9a70",
            "doctor_name": "\u9648\u56fd\u826f",
            "hospital_name": "\u6e05\u534e\u533b\u9662",
            "cid_name": "\u6d4b\u8bd5",
            "create_at": "2021-03-14 15:02:34",
            "treat_date": "2021-03-15",
            "time_segment": "2",
            "external_status": "4",
            "status": "4",
            "price": "0.01",
            "outpatient_type": "1",
            "uid": "**********",
            "is_can_refund": "1",
            "is_urgent": true
        }, {
            "bd_order_id": "svc_8518_1615793067731687",
            "patient_name": "\u8bf8\u845b\u4eae",
            "doctor_name": "\u9648\u56fd\u826f",
            "hospital_name": "\u6e05\u534e\u533b\u9662",
            "cid_name": "\u6d4b\u8bd5",
            "create_at": "2021-03-15 15:25:32",
            "treat_date": "2021-03-25",
            "time_segment": "1",
            "external_status": "4",
            "status": "4",
            "price": "0.10",
            "outpatient_type": "2",
            "uid": "**********",
            "is_can_refund": "1",
            "is_urgent": true
        }, {
            "bd_order_id": "svc_1615545315943405",
            "patient_name": "\u9a6c\u6668\u9a70",
            "doctor_name": "\u9648\u56fd\u826f",
            "hospital_name": "\u6e05\u534e\u533b\u9662",
            "cid_name": "SHOWCASE",
            "create_at": "2021-03-12 18:35:15",
            "treat_date": "2021-11-05",
            "time_segment": "2",
            "external_status": "14",
            "status": "6",
            "price": "0.20",
            "outpatient_type": "1",
            "uid": "**********",
            "is_can_refund": "0",
            "is_urgent": true
        }],
        "total": 18,
        "config": {
            "status_config": {
                "1": "\u5f85\u4ed8\u6b3e",
                "2": "\u60a3\u8005\u672a\u652f\u4ed8\u53d6\u6d88",
                "3": "\u8d85\u65f6\u53d6\u6d88",
                "4": "\u5df2\u4ed8\u6b3e",
                "5": "\u9000\u6b3e\u4e2d",
                "6": "\u5df2\u9000\u6b3e"
            },
            "c_status_config": {
                "1": "\u5f85\u652f\u4ed8",
                "2": "\u652f\u4ed8\u524d\u7528\u6237\u53d6\u6d88\uff1b",
                "3": "\u652f\u4ed8\u524d\u8d85\u65f6\u53d6\u6d88\uff1b",
                "4": "\u5df2\u652f\u4ed8\uff1b",
                "5": "TP\u65b9\u7533\u8bf7\u8865\u9000\u56de\u8c03\u901a\u77e5\uff0c\u9000\u6b3e\u4e2d",
                "6": "TP\u65b9\u7533\u8bf7\u8865\u9000\u56de\u8c03\u901a\u77e5\uff0c\u9000\u6b3e\u5b8c\u6210",
                "7": "\u652f\u4ed8\u540e\u7528\u6237\u53d6\u6d88\u4e0d\u9000\u6b3e",
                "8": "\u652f\u4ed8\u540e\u7528\u6237\u53d6\u6d88\u5e76\u9000\u6b3e\uff1b",
                "9": "\u8d85\u8fc7\u5c31\u8bca\u65f6\u95f4\u81ea\u52a8\u6838\u9500\u5b8c\u6210\uff1b",
                "10": "\u6838\u9500\u540e\u5df2\u8bc4\u4ef7\uff1b",
                "11": "\u652f\u4ed8\u540e\u7cfb\u7edf\u53d6\u6d88\u4e0d\u9000\u6b3e",
                "12": "\u652f\u4ed8\u540e\u7cfb\u7edf\u53d6\u6d88\u5e76\u9000\u6b3e",
                "13": "\u9000\u6b3e\u4e2d\uff1b",
                "14": "\u9000\u6b3e\u5b8c\u6210\uff0c\u6216\u8865\u9000\u5b8c\u6210\uff1b",
                "15": "TP\u65b9\u8981\u6c42\u9000\u6b3e",
                "16": "TP\u9000\u6b3e\u4e2d\uff0cc\u7aef\u5df2\u7ecf\u5728\u8d70\u9000\u6b3e\u6d41\u7a0b",
                "17": "\u8d85\u65f6\u53d6\u6d88"
            },
            "time_segment_config": {
                "1": "\u4e0a\u5348",
                "2": "\u4e0b\u5348",
                "3": "\u665a\u4e0a"
            },
            "outpatient_type_config": {
                "1": "\u4e13\u5bb6\u95e8\u8bca",
                "2": "\u666e\u901a\u95e8\u8bca"
            },
            "patient_type_config": ["\u521d\u8bca", "\u590d\u8bca"]
        }
    }
}
