module.exports = {
    'errno': 0,
    'errmsg': 'success',
    'data': {
        'status': 1, // 客户保障状态 -1:类目风险不明确 1：未缴纳保证金 2：保证金正常 3：已欠费（需补缴）4：未签署买家基础保障协议
        'statusDesc': '保证金正常',
        'depositBalance': 100000, // 保证金余额 单位：分
        'page': 1, // 当前页
        'pageNum': 2, // 总页数
        'limit': 1, // 每页个数
        'depositList': [
            {
                'seq': 3, // 序号
                'type': '网民权益保障', // 保证金类型
                'optime': '2023-07-19 14:56', // 操作时间
                'opt': '违规罚款', // 操作类型
                'money': '-1.00', //  操作金额 单位元
                'balance': '9998.00', //  剩余金额 单位元
                'orderId': '85413', //  订单号
                'customId': 'XYBZ_151059', //  客户ID
                'canExpand': true,
                'pictures': ['https://muzhi-public-pic-test.cdn.bcebos.com/100000005679', 'https://muzhi-public-pic-test.cdn.bcebos.com/100000005679',
                    'https://muzhi-public-pic-test.cdn.bcebos.com/100000005679'],
                'description': 'ceshi', // 描述
            },
        ],
    },
};