/**
 * @file 获取医生智能体
 * https://iapi.baidu-int.com/web/project/353705/apis/api-4665485
 */



module.exports = {
    'errno': 0,
    'errmsg': 'success',
    'data': {
        'base_info': {
            'id': 1624169574439470,
            'shoubai_c_appid': 1624169574439470,
            'user_id': 1740408078,
            'name': '杨庆玺医生',
            'wishes': '擅长心血管内科各种疾病的诊治',
            'description': '',
            'avatar': 'https://cambrian-images.cdn.bcebos.com/635cdd8ed6d35c5902914023bf73253c_1548922430681.jpeg',
            'type': 'individual',
            'sub_type': 'individual',
            'domain': '健康',
            'location': '山东省-泰安',
            'status': 'pass',
            'created_at': '2019-01-31 18:00:08',
            'updated_at': '2020-02-22 11:59:36',
            'audit_at': '2020-02-22 11:04:22',
            'audit_msg': '',
            'alive_check': 1,
            'authlist': '{"1":1,"6":1,"22":1}',
            'source_type': 'tp',
            'plantform_auth': 3,
            'auth_audit': 1,
            'auth_at': '2020-02-21 20:22:26',
        },
        'tag_info': {
            'finalstatus': '2',
            'finalrejreason': '{"rejid":[],"rest":""},{"rejid":[],"rest":""},{"rejid":[],"rest":""}',
            'docinfo': {
                'office': '内科',
                'status': '2',
                'rejreason': '{"rejid":[],"rest":""}',
            },
            'titleinfo': {
                'title': '副主任医师',
                'titlepic': [
                    'https://trust.baidu.com/cxtools/file/uuappic?fname=/comt/1551749450_6b49f5783b6be7b375dabbd7dbb28d5f.jpeg&bucket=vstarimg',
                    'https://trust.baidu.com/cxtools/file/uuappic?fname=/comt/1551749446_2507e880ac9c2d6d967072c7e4fa8cf1.jpeg&bucket=vstarimg',
                ],
                'status': '2',
                'rejreason': '{"rejid":[],"rest":""}',
            },
            'practiceinfo': {
                'practicenum': '110370000026290',
                'practicepic': [
                    'https://trust.baidu.com/cxtools/file/uuappic?fname=/comt/1551749666_44e1689932f8eaaf3f242847d8593d9b.jpeg&bucket=vstarimg',
                    'https://trust.baidu.com/cxtools/file/uuappic?fname=/comt/1551749669_9acfb7ad741fa0499f5921241f72dbff.jpeg&bucket=vstarimg',
                    'https://trust.baidu.com/cxtools/file/uuappic?fname=/comt/1551749914_7154da3d404b7f52f9e63e8de200c6a1.jpeg&bucket=vstarimg',
                    'https://trust.baidu.com/cxtools/file/uuappic?fname=/comt/1551749921_a260b814bce36ee0ae0567248e8cbc6f.jpeg&bucket=vstarimg',
                ],
                'hospital': '山东省泰山疗养院',
                'status': '2',
                'rejreason': '{"rejid":[],"rest":""}',
            },
        },
    },
};
