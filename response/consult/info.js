module.exports = {
    'status': 0,
    'toast': 'string',
    'msg': 'string',
    'lid': 'string',
    'applid': 'string',
    'data': {
        'lo_id': 'string',
        'so_id': '24235435',
        'patient_id': 12344,
        'qid_encode': 234234,
        'ly_status': 0,
        'uid': 234234,
        'ly_status_comment': 'string',
        'order_status': 1,
        'order_status_comment': 'string',
        'close_reason': 0,
        'close_reason_comment': 'string',
        'resource_id': 23,
        'resource_id_comment': 'string',
        'assistant_uid': '23',
        'expert_id': 12,
        'dr_uid': 23,
        'team_id': 4,
        'group_id': 'string',
        'dispatch_type': 2,
        'is_concentrate': 0,
        'is_self_employed': 0,
        'is_medical_cosmetology': 0,
        'create_time': **********,
        'accept_time': **********,
        'close_time': **********,
        'risk_tags': {
            'hit_abuse': 1,
            'hit_gift': 1,
            'hit_private_chat': 1,
            'hit_serious_illness': 1,
            'hit_prescribing': 0,
        },
        'e_star': 0,
        'qid': 23423423,
        'triage_content': 'string',
        'pack_id': 0,
        'refund_start_time': 0,
        'pre_lo_id': 23,

        'sku_id': 234,
        'sku_round_used': 2,
        'sku_time_limit': 100,
        'triage_cid1': 0,
        'triage_cid2': 0,
        'tirage_cid1_name': 'string',
        'tirage_cid2_name': 'string',
        'patient_sex': 'string',
        'patient_age': 18,
        'round_used': 5,
        'triage_pics': [
            'string',
        ],
        'dr_cid1': 0,
        'dr_cid2': 0,
        'dr_cid1_name': 'string',
        'dr_cid2_name': 'string',
        'relation_lo_id_list': [
            'string',
        ],
    },
};
