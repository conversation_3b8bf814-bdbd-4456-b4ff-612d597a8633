module.exports = {
    'status': 0,
    'toast': 'string',
    'msg': 'string',
    'lid': 'string',
    'applid': 'string',
    'data': {
        'pay_record': {
            'so_id': '4710034458379073846',
            'pay_order_id': 115431670226146,
            'pay_status': 1,
            'real_fee': 300,
            'total_fee': 500,
            'pay_time': 1723462814,
            'refund_status': 1,
            'refund_start_time': 1723462814,
            'refund_finish_time': 1723462814,
            'consume_time': 1723462814,
            'origin_price': 260,
            'platform_fee': 50,
        },
        'child_records': [
            {
                'child_so_id': '4710034458379173846',
                'so_id': '4710034458379073846',
                'pay_order_id': 115431670226147,
                'split_fee': 250,
                'split_time': 1723462814,
                'child_pay_status': 1,
                'charge_type': 1,
            },
        ],
    },
};

