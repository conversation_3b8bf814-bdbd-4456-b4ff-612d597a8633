module.exports = {
    'errno': 0,
    'errmsg': 'success',
    'data': {
        'issue_info': {
            'id': '396',
            'description': '1612337789TEST电话咨询最近吃瓜吃胀肚怎么办？',
            'tel_consult_id': '396',
            'uid': '1612337789',
            'doctor_id': '2500574176',
            'pack_brief': '电话咨询',
            'cname1': '儿科',
            'cname2': '小儿营养保健科',
            'pack_duration': '10',
            'create_time': '2021-02-03 15:36:29 ',
            'appointment_time': '2021-02-03 15:45:00 ',
            'callstart_time': '2021-02-03 15:45:00 ',
            'callend_time': '1970-01-01 08:00:00 ',
            'success_count': null,
            'bill_service_seconds': '0分0秒',
            'orderclose_type': '-',
            'order_status': '待认领',
            'pay_amount': 5,
            'pay_status_desc': '已支付',
            'ext_info': {
                'age': '21',
                'name': '电话接诊',
                'sex': '男',
                'role': '自己',
                'pic_urls': [
                    'http://hiphotos.baidu.com/zhidao/wh%3D450%2C600/sign=0a68f21fbe096b63814c56543903ab72/b64543a98226cffc1e398a82b9014a90f603eaea.jpg',
                    'http://hiphotos.baidu.com/zhidao/wh%3D450%2C600/sign=f331be096b63f6241c083107b274c7ce/42a98226cffc1e178bacb9014a90f603738de9ea.jpg',
                    'http://hiphotos.baidu.com/zhidao/wh%3D450%2C600/sign=bc276b63f6246b607b5bba70dec8367a/8326cffc1e178a82bb2f4a90f603738da977e8ea.jpg',
                    'http://hiphotos.baidu.com/zhidao/wh%3D450%2C600/sign=684df6246b600c33f02cd6cc2f7c7d39/cefc1e178a82b90149bef603738da9773912efea.jpg',
                    'http://hiphotos.baidu.com/zhidao/wh%3D450%2C600/sign=f20a6b600c3387449c902778643ff5cf/1f178a82b9014a90f22d738da9773912b31beeea.jpg',
                    'http://hiphotos.baidu.com/zhidao/wh%3D450%2C600/sign=6e4e0c338744ebf86d246c3becc9fb1c/8b82b9014a90f60376a3a9773912b31bb051edea.jpg',
                    'http://hiphotos.baidu.com/zhidao/wh%3D450%2C600/sign=0a1d8744ebf81a4c2667e4cde21a4c6f/b8014a90f603738daf593912b31bb051f819ecea.jpg',
                    'http://hiphotos.baidu.com/zhidao/wh%3D450%2C600/sign=806aebf81a4c510fae91ea1e5569091b/4b90f603738da9773e3cb31bb051f8198618e3ea.jpg',
                ],
            },
            'confirm_data': {
                'tips': 'hahahah',
                'is_show': 1,
                'date_info': {
                    'minute_list': [
                        {
                            'text': '0分',
                            'value': '0',
                        },
                        {
                            'text': '10分',
                            'value': '10',
                        },
                        {
                            'text': '20分',
                            'value': '20',
                        },
                        {
                            'text': '30分',
                            'value': '30',
                        },
                        {
                            'text': '40分',
                            'value': '40',
                        },
                        {
                            'text': '50分',
                            'value': '50',
                        },
                    ],
                    'date_list': [
                        {
                            'text': '02-03',
                            'value': '2021-02-03',
                            'hour_list': [
                                {
                                    'text': '06点',
                                    'value': '06',
                                },
                                {
                                    'text': '07点',
                                    'value': '07',
                                },
                            ],
                        },
                        {
                            'text': '02-04',
                            'value': '2021-02-04',
                            'hour_list': [
                                {
                                    'text': '06点',
                                    'value': '06',
                                },
                                {
                                    'text': '07点',
                                    'value': '07',
                                },
                            ],
                        },
                    ],
                },
                'tel_appointment_at': '2021-02-03 06:40:00',
            },
            'doctor_info': {
                'dr_uid': '2500574176',
                'cid_info': '外科-骨科',
                'real_name': '陈国良',
            },
        },
        'doctor_summary': '',
        'call_log': [{
            url: 'https://wlyxvpcdn.xiaoben365.com/wlyx/xbyx/2020/wu_1e2fi9sellrj1ugu1f8v10bj1ph31l/lq.mp4',
        }],
    },
};
