module.exports = {
    'errno': 0,
    'errmsg': 'success',
    'data': {
        'list': [
            {
                'id': 1,
                'dr_uid': '医生 UID', // 标题
                'dr_info_text': '张飞 北京口腔医院 内科', // 医生信息文本内容：医生姓名 医院 科室
                'voice_url': 'baidu.com/12345667', // 录音文件地址，点击播放录音
                'voice_time': 12, // 录音时长，单位 s
                'voice_asr_content': '你好', // 欢迎语录音自动转成的文字：
                'created_time': '2024-05-27 15:31:38', // 提交时间：医生提交的时间，年-月-日 时:分:秒
                'checked_time': '2024-05-27 15:31:38', // 审核时间：取自动审核/人工审核最新一次审核的时间，年-月-日 时:分:秒
                'checked_status': 1, // 审核状态
                'checked_status_text': '审核通过',
                'not_passed_reason': '私联', // 审核未通过原因
                'checked_by': '系统', // 审核人。如果是默认通过，审核人展示为系统，若运营进行了审核，则展示具体的人
            },
            {
                'id': 2,
                'dr_uid': '医生 UID', // 标题
                'dr_info_text': '张飞 北京口腔医院 内科', // 医生信息文本内容：医生姓名 医院 科室
                'voice_url': 'baidu.com/12345667', // 录音文件地址，点击播放录音
                'voice_time': 12, // 录音时长，单位 s
                'voice_asr_content': '你好', // 欢迎语录音自动转成的文字：
                'created_time': '2024-05-27 15:31:38', // 提交时间：医生提交的时间，年-月-日 时:分:秒
                'checked_time': '2024-05-27 15:31:38', // 审核时间：取自动审核/人工审核最新一次审核的时间，年-月-日 时:分:秒
                'checked_status': 2, // 审核状态
                'checked_status_text': '审核通过',
                'not_passed_reason': '私联', // 审核未通过原因
                'checked_by': '系统', // 审核人。如果是默认通过，审核人展示为系统，若运营进行了审核，则展示具体的人
            },
            {
                'id': 3,
                'dr_uid': '医生 UID', // 标题
                'dr_info_text': '张飞 北京口腔医院 内科', // 医生信息文本内容：医生姓名 医院 科室
                'voice_url': 'baidu.com/12345667', // 录音文件地址，点击播放录音
                'voice_time': 12, // 录音时长，单位 s
                'voice_asr_content': '你好', // 欢迎语录音自动转成的文字：
                'created_time': '2024-05-27 15:31:38', // 提交时间：医生提交的时间，年-月-日 时:分:秒
                'checked_time': '2024-05-27 15:31:38', // 审核时间：取自动审核/人工审核最新一次审核的时间，年-月-日 时:分:秒
                'checked_status': 0, // 审核状态
                'checked_status_text': '审核通过',
                'not_passed_reason': '私联', // 审核未通过原因
                'checked_by': '系统', // 审核人。如果是默认通过，审核人展示为系统，若运营进行了审核，则展示具体的人
            },
        ],
        'total': 100, // 总数
    },
};
