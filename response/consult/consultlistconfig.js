/*
 * @Descripttion: descripttion
 * @Author: <EMAIL>
 * @Date: 2021-04-16 11:06:12
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2021-04-16 11:06:34
 */
module.exports = {
    errno: 0,
    errmsg: 'success',
    data: {
        category_items: {
            0: '按时咨询',
            1: '按时付费',
            2: '按次付费',
            3: '电话咨询',
        },
        pack_items: {
            101: '按时咨询服务包',
            201: '20轮/24小时服务包',
            202: '3轮/2小时服务包',
            203: '[大搜专家-免费]不限轮次/2小时服务包',
            204: '[大搜专家-三甲]不限轮次/2小时服务包',
            205: '[大搜专家-二甲]不限轮次/2小时服务包',
            206: '大搜专家定向付费20轮/24小时服务包',
            207: '大搜专家不限轮次/30分钟服务包',
            208: '互联网医院服务包',
            209: '北京医师会服务包',
            210: '海外咨询服务包',
            211: '重症专区服务包',
            212: '补方咨询服务包（用于药品带入直接开处方）',
            213: '开药门诊',
            214: '带药门诊',
            215: '春节活动三甲免费包',
            216: '追问包',
            217: '赠送包',
            233: '定向义诊',
        },
        pay_status_items: {
            0: '无需支付',
            1: '已支付',
            2: '已免单',
            3: '未支付',
            4: '已退款',
            5: '退款进行中',
        },
        status_items: {
            0: '待激活',
            1: '待认领',
            2: '已认领',
            3: '医生活跃',
            4: '患者活跃',
            5: '双边活跃',
            6: '等待确认',
            7: '待结算',
            15: '咨询冻结',
            16: '已关闭',
            17: '等待用户同意',
            18: '服务进行中',
            99: '删除',
        },
        close_type_items: {
            300: '正常支付关闭',
            301: '用户投诉关闭',
            302: '医生超时关闭',
            303: '用户同意转诊',
            304: '用户拒绝转诊',
            305: '用户同意转诊但达到了转诊次数上限',
            306: '用户同意结束',
            307: '用户拒绝结束',
            308: '用户不想等待',
            309: '患者主动结束',
            310: '医生跳过次数达到了上限',
            311: '患者超时关闭',
            312: 'mis后台运营操作免单而关闭',
            313: '医生主动结束',
            314: '用户取消自动分诊到咨询',
            315: '患者结束等待',
            316: '到达服务时长',
            317: '到达最大轮次',
            318: '医生转诊关闭',
            319: '医生超时释放',
            320: '医生拒绝接诊',
            323: '正常关闭',
            324: '正常关闭',
            325: '通话连接失败',
            398: '无人接诊关闭',
            399: '出现异常关闭',
            401: '医生退诊结束',
            402: '医患协调时间失败',
            403: '运营关闭订单',
        },
        new_close_type_items: {
            11: '轮次关单',
            12: '时长关单',
            21: '医生主动关单',
            22: '医生转诊',
            23: '医生拒诊',
            24: '医生退诊',
            25: '医生废弃（标记非医疗）',
            31: '超时未接诊关单',
            32: '接诊后超时未服务关单',
            33: '超时未履约关单',
            34: '拒绝医生接诊，如接诊时判定医生在黑名单，或者医生不属于团队',
            41: '用户退款',
            51: '客服退款',
        },
        order_status_items: {
            0: '已使用',
            1: '已支付',
            2: '未支付',
            3: '申请退款',
            4: '已退款',
            10: '支付失败',
            40: '退款失败',
            99: '删除',
            100: '异常',
        },
        dispatch_type_items: {
            0: '拉题',
            1: '值班派题',
            2: '直连派题',
            9: '定向',
        },
        scrid_items: {
            0: '问医生PC端',
            1: '问医生WAP端',
            2: '基础知道NA端转入',
            3: '母婴',
            4: '基础知道PC新问题的转入',
            5: '基础知道NA新问题的转入',
            6: '基础知道WAP新问题的转入',
            7: 'ps医疗卡片',
            8: 'WAP端wise引导问题',
            9: '高质量问题挖掘(实验数据）',
            10: '从向专家提问的问题转化而来',
            11: '从活动提问转化而来',
            12: '从百度医生提问入口而来',
            13: '大搜专家平台',
            14: '熊账号来源',
            15: '大搜MIP来源',
            20: '问医生APP Android',
            21: '问医生APP IOS',
            22: '问医生APS Android',
            101: '知识库问题导入',
            102: '知识库合作医院问题导入',
            103: '机构问题导入',
            104: '合作机构',
            105: '个人医生',
            1001: '高质量问题导入',
        },
        lv_status_items: {
            11: '待分发',
            12: '分发中',
            13: '已分发资源方',
            14: '已分发医生',
            15: '诊前回收',
            16: '拒诊回收[非定向]',
            21: '已接诊待回复',
            22: '医生首回开始服务',
            31: '正常关单',
            32: '异常关单',
        },
        risk_tags_items: {
            hit_abuse: '命中辱骂',
            hit_private_chat: '命中私联',
            hit_serious_illness: '命中重症',
            0: '未命中',
            1: '命中',
        },
        create_type_items: {
            0: '通用类型',
            1: '转诊类型',
        },
        new_pay_status_items: {
            1: '待支付',
            2: '已支付',
            3: '已核销',
            4: '退款中',
            5: '退款完成',
            6: '退款失败',
        },
        sc_status_mapping: {
            11: '待分发',
            12: '分发中',
            13: '已分发资源方',
            14: '已分发医生',
            15: '诊前回收',
            16: '拒诊回收[非定向]',
            21: '已接诊待回复',
            22: '医生首回开始服务',
            31: '正常关单',
            32: '异常关单',
        },
    },
};
