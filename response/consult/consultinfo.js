/* eslint-disable vue/max-len */
/* eslint-disable max-len */
module.exports = {
    errno: 0,
    errmsg: 'success',
    data: {
        lo_id: '96fb8907-1ae1-44e2-867e-f6ee87739d41_29',
        qid: 1231231213,
        so_id: '1231231213',
        consult_id: 34234234,
        group_id: '1231231213',
        format_consult: {
            suggest_amount: '0元',
            pay_amount: '0元',
            coupon_amount: '未使用抵扣券',
            balance_amount: '0赞',
            pay_status_desc: '无需支付',
            close_type_desc: null,
            // 'status_desc': '服务进行中',
            pack_brief: '30分钟',
            bill_service_seconds: 0,
            user_rounds_cnt: 1,
            category: 2,
            consult_id: 126116345,
            close_at: 0,
            // ===> 新加的==== 覆盖以上的
            // 'suggest_amount': '0.3元',
            // 'pay_amount': '0.3元',
            // 'coupon_amount': '0元',
            // 'balance_amount': '0赞',
            // 'pay_status_desc': '已支付', // 支付状态
            // 'close_type_desc': '医生主动结束', // 咨询关闭类型
            // 'close_at': 1617800454, // 用户主动关闭时间戳
            // 'status_desc': '已关闭', // 咨询状态
            // 'pack_brief': '3轮\/7天', // 服务包简介
            // 'bill_service_seconds': 0, // 服务时长(秒)
            // 'user_rounds_cnt': 0,
            // 'category': 2,
            category_desc: '按次付费', // 咨询类型
            srcid_desc: '大搜专家平台', // 咨询来源
            // 'consult_id': 10026415, // 咨询ID
            assistant_name: '医生本人',
            is_not_medical: 0, // 是否非医疗问题 0否 1是
            dispatch_type_desc: '派题', // 分发类型
            claim_at: **********, // 医生认领时间戳（接诊时间
            delete_flag: 0, // 拉题是否被删过 1是 0否
            dr_active: **********, // 医生是否说过话 >0就是说过话
            user_active: 0, // 患者是否说过话 >0就是说过话
            service_start: **********, // 服务开始时间
            zj_qid: null, // C端的QID
            uid: **********, // 提问用户uid
            patient_id: 'normal_128', // 患者ID
            dr_uid: **********, // 医生UID
            team_id: 100001, // 团队ID
            doctor_rounds_cnt: '2', // 医生患者对话轮数
            avg_rounds_time: '1', // 平均回复时长
            dispatch_cid1: '121', // 一级科室id
            dispatch_cid1_name: '妇产科', // 一级科室名称
            dispatch_cid2: '1', // 二级科室id
            dispatch_cid2_name: '妇科', // 二级科室名称
            channel_type_name: '咨询渠道', // 咨询渠道
            trans_consult_id: '222160312',
        },
        issue_info: {
            is_report: true,
            is_prescription_doctor: 0,
            issue_id: 320100596,
            uid: '**********',
            cid1: 1255,
            cid2: 125502,
            cid1_name: '眼科',
            cid2_name: '眼科',
            qid: '**********',
            issue_tags: [],
            description: '血红蛋白低怎么回事,早晨起床眼睛肿脸肿',
            status: 0,
            stage: 4,
            create_at: 1610953034,
            update_at: 1610953034,
            ext_info: {
                sex: '男',
                gender: '男',
                age: '58',
                pic_urls: [
                    {
                        origin: 'https://bj.bcebos.com/muzhi-pic/100026584511?authorization=bce-auth-v1%2F640c67afd1de40d2a7621c76cca1ea63%2F2021-01-18T07%3A19%3A04Z%2F-1%2Fhost%2F7a03e9f9bbfa59c34d29c9a406b0e43709effc2dcd7e585e4e72856c63ad2dbf',
                        w600h800:
                            'https://bj.bcebos.com/muzhi-pic/100026584511%40w_1000%2Ch_2000?authorization=bce-auth-v1%2F640c67afd1de40d2a7621c76cca1ea63%2F2021-01-18T07%3A19%3A04Z%2F-1%2Fhost%2F1ff87f2ba336a2bc918b44abec94a0eafaadcdce2e21be15dce1cb0b60389d12',
                        thumb: 'https://bj.bcebos.com/muzhi-pic/100026584511%40w_60%2Ch_80?authorization=bce-auth-v1%2F640c67afd1de40d2a7621c76cca1ea63%2F2021-01-18T07%3A19%3A04Z%2F3600%2Fhost%2F9ff56973adae7912b08284d43ab3951935ef61b6c69553fef75d79a7d863eb6a',
                    },
                    {
                        origin: 'https://bj.bcebos.com/muzhi-pic/100026584511?authorization=bce-auth-v1%2F640c67afd1de40d2a7621c76cca1ea63%2F2021-01-18T07%3A19%3A04Z%2F-1%2Fhost%2F7a03e9f9bbfa59c34d29c9a406b0e43709effc2dcd7e585e4e72856c63ad2dbf',
                        w600h800:
                            'https://bj.bcebos.com/muzhi-pic/100026584511%40w_1000%2Ch_2000?authorization=bce-auth-v1%2F640c67afd1de40d2a7621c76cca1ea63%2F2021-01-18T07%3A19%3A04Z%2F-1%2Fhost%2F1ff87f2ba336a2bc918b44abec94a0eafaadcdce2e21be15dce1cb0b60389d12',
                        thumb: 'https://bj.bcebos.com/muzhi-pic/100026584511%40w_60%2Ch_80?authorization=bce-auth-v1%2F640c67afd1de40d2a7621c76cca1ea63%2F2021-01-18T07%3A19%3A04Z%2F3600%2Fhost%2F9ff56973adae7912b08284d43ab3951935ef61b6c69553fef75d79a7d863eb6a',
                    },
                    {
                        origin: 'https://bj.bcebos.com/muzhi-pic/100026584511?authorization=bce-auth-v1%2F640c67afd1de40d2a7621c76cca1ea63%2F2021-01-18T07%3A19%3A04Z%2F-1%2Fhost%2F7a03e9f9bbfa59c34d29c9a406b0e43709effc2dcd7e585e4e72856c63ad2dbf',
                        w600h800:
                            'https://bj.bcebos.com/muzhi-pic/100026584511%40w_1000%2Ch_2000?authorization=bce-auth-v1%2F640c67afd1de40d2a7621c76cca1ea63%2F2021-01-18T07%3A19%3A04Z%2F-1%2Fhost%2F1ff87f2ba336a2bc918b44abec94a0eafaadcdce2e21be15dce1cb0b60389d12',
                        thumb: 'https://bj.bcebos.com/muzhi-pic/100026584511%40w_60%2Ch_80?authorization=bce-auth-v1%2F640c67afd1de40d2a7621c76cca1ea63%2F2021-01-18T07%3A19%3A04Z%2F3600%2Fhost%2F9ff56973adae7912b08284d43ab3951935ef61b6c69553fef75d79a7d863eb6a',
                    },
                ],
                hospital: '',
                go_to_doctor: 0,
                member_id: 0,
                role: '自己',
                name: '',
                birthday: 0,
                localtion: '',
                src: '',
                user_status: '',
            },
            user_require: [],
            prime_summary: {
                prime_id: 0,
                talk_id: 0,
                prime_summary_id: 0,
                is_confirmed: 0,
                illnesses: [],
                urgency_level: 0,
                risk_level: 0,
                remission_level: 0,
                status: 0,
                create_at: 0,
                summary_desc: '',
            },
        },
        inspect_info: {
            is_direct_consult_check: false,
            base_info: {
                inspect_dr_name: '张三',
                inspect_time: '0',
                review_uname: '李四',
                review_time: '0',
                review_change_time: '0',
            },
            base_line_info: {
                inspect_result_desc: '不合格',
                inspect_reason: '问题222的原因',
                review_result_desc: '质检正确',
                review_reason: '问题原因2222',
                final_inspect_result_desc: '质检正确',
                final_inspect_reason: '原因说明3333',
                final_result_desc: '合格',
            },
            profession_line_info: {
                inspect_profession_result_desc: '80分',
                inspect_profession_reason: '问题222的原因',
                review_profession_result_desc: '质检正确',
                review_profession_reason: '问题原因2222',
                final_inspect_profession_result_desc: '质检正确',
                final_inspect_profession_reason: '原因说明3333',
                final_profession_result_desc: '90分',
            },
            service_line_info: {
                inspect_service_result_desc: '不合格',
                inspect_service_result_reason: '问题222的原因',
                review_service_result_desc: '质检正确',
                review_service_reason: '问题原因2222',
                final_inspect_service_result_desc: '质检正确',
                final_inspect_service_reason: '原因说明3333',
                final_service_result_desc: '95分',
            },
        },
        msg_list: [
            {
                msg_id: 1428614555,
                talk_id: 111925800,
                type: 3,
                user_type: 3,
                text: '(系统自动询问)\n请问这种情况具体多久了？',
                pic_url: [],
                user_info: {
                    uid: '************',
                    name: '芦桂珍',
                    title: '主治医师',
                    avatar: 'https://muzhi-public-pic.cdn.bcebos.com/5785',
                    clinical_title: '主治医师',
                    field_text: '医师',
                    online_status: 0,
                    type: 3,
                },
                post_name: '**********',
                is_doctor: 1,
                time: '2021-01-18 14:57:24',
                audio_url:
                    'https://bj.bcebos.com/muzhi-audio/100071466909?authorization=bce-auth-v1%2F640c67afd1de40d2a7621c76cca1ea63%2F2022-04-18T09%3A15%3A49Z%2F3600%2Fhost%2Fd3b4152b941de337877c391d2baabeca4f4b7d3f0073192b9f8d4513934c1703',
                audio_id: 0,
                object_id: '',
                duration: 0,
                associate_info: [],
            },
            {
                msg_id: 1428615222,
                talk_id: 111925800,
                type: 16,
                user_type: 2,
                text: '2~7天',
                pic_url: [],
                post_name: '**********',
                is_doctor: 0,
                time: '2021-01-18 14:57:39',
                audio_id: 0,
                object_id: '',
                duration: 0,
                associate_info: {
                    type: '1223',
                },
            },
            {
                msg_id: 1428615227,
                talk_id: 111925800,
                type: 1,
                user_type: 3,
                text: '(系统自动询问)\n还有其他症状吗？',
                pic_url: [],
                post_name: '**********',
                is_doctor: 1,
                time: '2021-01-18 14:57:39',
                audio_id: 0,
                object_id: '',
                duration: 0,
                associate_info: [],
            },
            {
                msg_id: 1428615661,
                talk_id: 111925800,
                type: 1,
                user_type: 2,
                text: '没有',
                pic_url: [],
                post_name: '**********',
                is_doctor: 0,
                time: '2021-01-18 14:57:50',
                audio_id: 0,
                object_id: '',
                duration: 0,
                associate_info: [],
            },
            {
                msg_id: 1428619398,
                talk_id: 111925800,
                type: 1,
                user_type: 2,
                text: '目前没有其他症状',
                pic_url: [],
                post_name: '**********',
                is_doctor: 0,
                time: '2021-01-18 14:59:20',
                audio_id: 0,
                object_id: '',
                duration: 0,
                associate_info: [],
            },
            {
                msg_id: 1428620943,
                talk_id: 111925800,
                type: 1,
                user_type: 3,
                text: '您好，我是王芳医生，现在由于咨询患者较多。需要按程序回复。可能出现回复不及时。你先描述你的症状，方便我及时回复消息。',
                pic_url: [],
                post_name: '**********',
                is_doctor: 1,
                time: '2021-01-18 15:00:03',
                audio_id: 0,
                object_id: '',
                duration: 0,
                associate_info: [],
            },
            {
                msg_id: 1428621805,
                talk_id: 111925800,
                type: 1,
                user_type: 3,
                text: '眼睛浮肿的话主要是因为疾病，包括眼部外伤啊，炎症啊，过敏啊，营养不良或者血管性水肿。心肾功能损害的还有脾虚，湿热都有可能引起水肿的。要去内科做检查。',
                pic_url: [],
                post_name: '**********',
                is_doctor: 1,
                time: '2021-01-18 15:00:14',
                audio_id: 0,
                object_id: '',
                duration: 0,
                associate_info: [],
            },
            {
                msg_id: 1428621805,
                talk_id: 111925800,
                type: 305,
                user_type: 3,
                text: '眼睛浮肿的话主要是因为疾病，包括眼部外伤啊，炎症啊，过敏啊，营养不良或者血管性水肿。心肾功能损害的还有脾虚，湿热都有可能引起水肿的。要去内科做检查。',
                pic_url: [],
                post_name: '**********',
                is_doctor: 1,
                time: '2021-01-18 15:00:14',
                audio_id: 0,
                object_id: '',
                duration: 0,
                call_time: '08:00',
                call_url: '',
                call_stastus: 24,
                associate_info: [],
            },
            {
                msg_id: 2222,
                talk_id: 5024675,
                consult_id: 0,
                is_advice: 0,
                type: 305,
                url: 'https://www.baidu.com',
                // eslint-disable-next-line vue/max-len
                text: '过去的我们很快乐，但是现在我们不能兼容了。',
                pic_url: [],
                user_info: {
                    uid: '**********',
                    patient_id: 'ct_1597065984511639',
                    name: 'momo',
                    alias: '',
                    mobile: '',
                    avatar: '',
                    type: 2,
                },
                time: **********,
                associate_info: '',
                audio_id: '',
                duration: 0,
                is_audio_played: 0,
                user_type: 2,
                call_stastus: 21,
            },
            {
                msg_id: **********,
                talk_id: 111925800,
                type: 1,
                user_type: 3,
                text: '做了肾内科方的面的检查吗？考虑肾炎',
                pic_url: [],
                post_name: '**********',
                is_doctor: 1,
                time: '2021-01-18 15:00:23',
                audio_id: 0,
                object_id: '',
                duration: 0,
                associate_info: [],
            },
            {
                msg_id: 1428623698,
                talk_id: 111925800,
                type: 1,
                user_type: 2,
                text: '最近几天起床后眼睛和脸有点肿',
                pic_url: [],
                post_name: '**********',
                is_doctor: 0,
                time: '2021-01-18 15:00:56',
                audio_id: 0,
                object_id: '',
                duration: 0,
                associate_info: [],
            },
            {
                msg_id: 1428624915,
                talk_id: 111925800,
                type: 2,
                user_type: 2,
                text: '',
                pic_url: {
                    origin: 'https://bj.bcebos.com/muzhi-pic/100026584511?authorization=bce-auth-v1%2F640c67afd1de40d2a7621c76cca1ea63%2F2021-01-18T07%3A19%3A04Z%2F-1%2Fhost%2F7a03e9f9bbfa59c34d29c9a406b0e43709effc2dcd7e585e4e72856c63ad2dbf',
                    w600h800:
                        'https://bj.bcebos.com/muzhi-pic/100026584511%40w_1000%2Ch_2000?authorization=bce-auth-v1%2F640c67afd1de40d2a7621c76cca1ea63%2F2021-01-18T07%3A19%3A04Z%2F-1%2Fhost%2F1ff87f2ba336a2bc918b44abec94a0eafaadcdce2e21be15dce1cb0b60389d12',
                    thumb: 'https://bj.bcebos.com/muzhi-pic/100026584511%40w_60%2Ch_80?authorization=bce-auth-v1%2F640c67afd1de40d2a7621c76cca1ea63%2F2021-01-18T07%3A19%3A04Z%2F3600%2Fhost%2F9ff56973adae7912b08284d43ab3951935ef61b6c69553fef75d79a7d863eb6a',
                },
                post_name: '**********',
                is_doctor: 0,
                time: '2021-01-18 15:01:24',
                audio_id: 0,
                object_id: '',
                duration: 0,
                associate_info: [],
            },
            {
                msg_id: 1428629376,
                talk_id: 111925800,
                type: 1,
                user_type: 2,
                text: '这是今早的化验单',
                pic_url: [],
                post_name: '**********',
                is_doctor: 0,
                time: '2021-01-18 15:03:16',
                audio_id: 0,
                object_id: '',
                duration: 0,
                associate_info: [],
            },
            {
                msg_id: 1428634163,
                talk_id: 111925800,
                type: 1,
                user_type: 2,
                text: '好慢啊',
                pic_url: [],
                post_name: '**********',
                is_doctor: 0,
                time: '2021-01-18 15:05:12',
                audio_id: 0,
                object_id: '',
                duration: 0,
                associate_info: [],
            },
            {
                msg_id: 1428641344,
                talk_id: 111925800,
                type: 1,
                user_type: 2,
                text: '😞😞',
                pic_url: [],
                post_name: '**********',
                is_doctor: 0,
                time: '2021-01-18 15:08:09',
                audio_id: 0,
                object_id: '',
                duration: 0,
                associate_info: [],
            },
            {
                msg_id: 1428647965,
                talk_id: 111925800,
                type: 1,
                user_type: 2,
                text: '😓',
                pic_url: [],
                post_name: '**********',
                is_doctor: 0,
                time: '2021-01-18 15:10:45',
                audio_id: 0,
                object_id: '',
                duration: 0,
                associate_info: [],
            },
            {
                msg_id: 1428654222,
                talk_id: 111925800,
                type: 1,
                user_type: 3,
                text: '看到了。',
                pic_url: [],
                post_name: '**********',
                is_doctor: 1,
                time: '2021-01-18 15:13:13',
                audio_id: 0,
                object_id: '',
                duration: 0,
                associate_info: [],
            },
            {
                msg_id: 1428656144,
                talk_id: 111925800,
                type: 1,
                user_type: 3,
                text: '有些贫血。',
                pic_url: [],
                post_name: '**********',
                is_doctor: 1,
                time: '2021-01-18 15:14:04',
                audio_id: 0,
                object_id: '',
                duration: 0,
                associate_info: [],
            },
            {
                msg_id: 1428656145,
                talk_id: 111925800,
                type: 29,
                user_type: 9,
                post_name: '2500574177',
                text: '',
                pic_url: [],
                is_doctor: 1,
                time: '2022-11-28 18:33:11',
                audio_id: 0,
                object_id: '',
                duration: 0,
                video_info: {
                    can_save: 0,
                    origin: 'https://bj.bcebos.com/muzhi-pic-test/20221125/1004_b_video_166937616576649321?authorization=bce-auth-v1%2F640c67afd1de40d2a7621c76cca1ea63%2F2022-12-01T05%3A56%3A00Z%2F3600%2Fhost%2Fe5b81e6826562f0e2c25b42063e681da1c7d07494ee99421214ef2d261739e5c',
                    thumb: 'https://bj.bcebos.com/muzhi-pic-test/22651?authorization=bce-auth-v1%2F640c67afd1de40d2a7621c76cca1ea63%2F2022-12-01T05%3A56%3A00Z%2F3600%2Fhost%2F4e7153ecc24478e7b8aea096c8e414d586ac93111bf7e15965f7b1b41788684a',
                    width: 200,
                    height: 200,
                    duration: 1000,
                    size: 6,
                },
                associate_info: {
                    video_object_id: '20221125/1004_b_video_166937616576649321',
                    video_thumb_id: '22651',
                    video_width: 200,
                    video_height: 200,
                    video_size: 1000,
                    video_duration: 6,
                },
                recalled: 0,
                user_info: {
                    uid: '2500574177',
                    name: '李旭',
                    title: '主任医师',
                    avatar: 'https://bj.bcebos.com/muzhi-public/46719',
                    clinical_title: '主任医师',
                    field_text: '医师',
                    online_status: 0,
                    type: 3,
                },
            },
            {
                msg_id: 3467577803,
                talk_id: 215774513,
                type: 305,
                user_type: 1,
                post_name: '',
                text: '',
                pic_url: [],
                is_doctor: 0,
                time: '2023-02-22 08:09:17',
                audio_id: 0,
                object_id: '',
                duration: 0,
                call_status: '24',
                call_info: '本次通话已结束，通话时长:02:59',
                call_time: '02:59',
                call_url:
                    'http://bj.bcebos.com/v1/cp-privacy/2/2023/02/22/52023022208055810670747320_95092120_13452345946_20230222080558_BD70.wav?authorization=bce-auth-v1%2F61bf983af7ca4d7fa6412ed517c63e3e%2F2023-02-22T00%3A13%3A19Z%2F-1%2F%2F7490a72b98a5ab49c1f5cee593947759a060c2219dd77602c34eed37d3431698',
                associate_info: {
                    uuid: '66e3564bef28f79ddb5ad388bc969efc',
                    call_status: '24',
                    call_time: '02:59',
                    call_info: '本次通话已结束，通话时长:02:59',
                },
                recalled: 0,
                user_info: null,
            },
        ],
        im_msg_list: [
            {
                msg_id: '839010591287806605',
                talk_id: 229466648,
                card_id: 4,
                card_object:
                        '{"text":"\\u60a3\\u8005\\u5df2\\u8d2d\\u4e70\\u975e\\u5b9a\\u5411\\u56fe\\u6587\\u95ee\\u8bca\\u670d\\u52a1"}',
                user_info: {
                    uid: '0',
                    name: '',
                    avatar: '',
                    type: 1,
                },
                assistant_uid: 0,
                time: 1698203161,
                msg_type: 18,
                msg_key: 'consultService:427c4615f5f2642219fbbdd295ccf642',
                recalled: 0,
                sort: 1698203161505,
                offset: 1698203161505,
                quotable: 0,
            },
            {
                msg_id: '839010591289903757',
                talk_id: 229466648,
                card_id: 6,
                card_object:
                        '{"head":{"title":"\\u6781\\u901f\\u56fe\\u6587","title_tag":"","action":""},"body":{"content_list":[{"title":"\\u60a3\\u8005\\u4fe1\\u606f","content":"\\u5f20\\u91d1\\u51e4 \\u5973 21\\u5c81"},{"title":"\\u670d\\u52a1\\u7c7b\\u578b","content":"15\\u5206\\u949f"},{"title":"\\u75c5\\u60c5\\u63cf\\u8ff0","content":"\\u5403\\u4e86\\u80c3\\u836f\\u7a81\\u7136\\u5598\\u4e0d\\u4e0a\\u6c14"}]},"buttons":[]}',
                user_info: {
                    uid: '4161316727',
                    name: '张金凤',
                    alias: '',
                    mobile: '',
                    avatar: '',
                    type: 2,
                },
                assistant_uid: 0,
                time: 1698203161,
                msg_type: 31,
                msg_key: 'consultService:427c4615f5f2642219fbbdd295ccf642',
                recalled: 0,
                sort: 1698203161510,
                offset: 1698203161510,
                quotable: 0,
            },
            {
                msg_id: '839000764394244743',
                talk_id: 229466648,
                card_id: 5,
                card_object:
                        '{"text":"\\u54a8\\u8be2\\u670d\\u52a1\\u5df2\\u5f00\\u59cb\\uff0c\\u533b\\u60a3\\u4ea4\\u6d41\\u5c06\\u4e8e15\\u5206\\u949f\\u540e\\u7ed3\\u675f"}',
                user_info: {
                    uid: '0',
                    name: '',
                    avatar: '',
                    type: 1,
                },
                assistant_uid: 0,
                time: 1698203170,
                msg_type: 14,
                msg_key: 'consultService:e4487e2ca7f77f37ab2387a6703712fa',
                recalled: 0,
                sort: 1698203170465,
                offset: 1698203170465,
                quotable: 0,
            },
            {
                msg_id: '839010642850482829',
                talk_id: 229466648,
                card_id: 1,
                card_object:
                        '{"text":"\\u60a8\\u597d\\uff0c\\u6211\\u662f\\u767e\\u5ea6\\u5065\\u5eb7\\u674e\\u6811\\u658c\\u533b\\u751f\\uff0c\\u975e\\u5e38\\u9ad8\\u5174\\u80fd\\u591f\\u4e3a\\u60a8\\u63d0\\u4f9b\\u533b\\u7597\\u54a8\\u8be2\\u670d\\u52a1\\u3002","quote":null}',
                user_info: {
                    uid: '5994659256',
                    name: '李树斌',
                    title: '主任医师',
                    avatar: 'https://muzhi-public-pic.cdn.bcebos.com/100080422047',
                    clinical_title: '主任医师',
                    field_text: '医师',
                    online_status: 0,
                    type: 3,
                },
                assistant_uid: 0,
                time: 1698203171,
                msg_type: 1,
                msg_key: 'consultService:c438bac6e286af7122cfd93f706e9c55',
                recalled: 0,
                sort: 1698203171836,
                offset: 1698203171836,
                quotable: 1,
            },
            {
                msg_id: '840978665555626624',
                talk_id: 229466648,
                card_id: 1,
                card_object: '{"text":"\\u4f60\\u597d","quote":null}',
                user_info: {
                    uid: '4161316727',
                    name: '张金凤',
                    alias: '',
                    mobile: '',
                    avatar: '',
                    type: 2,
                },
                assistant_uid: 0,
                time: 1698203178,
                msg_type: 1,
                msg_key: '2j1JaoU3WW',
                recalled: 0,
                sort: 1698203178066,
                offset: 1698203178387,
                quotable: 1,
            },
        ],
        summary: [],
        appeal_info: [],
        treat_assign_info: [],
        mainsuit_info: [],
        sub_consult_ids: ['126116345'],
        talk_id: 221,
        order_info: {
            order_id: '15878515',
            service_id: '10026415',
            origin_amount: '30', // 原始金额
            discount: '0',
            pay_amount: '30',
            balance_amount: '0',
            coupon_amount: '0',
            coupon_ids: '',
            pay_time: '**********',
            pay_uid: '**********',
            dr_uid: '0',
            pay_no: '********',
            pay_channel: '4',
            refund_amount: '0',
            refund_time: '0',
            notify_type: '0',
            writeoff: null,
            type: '7',
            ext_info: '',
            status: '1',
            enter_account: '0',
            create_at: '**********',
            update_at: '**********',
            status_desc: '已支付', // 订单的状态
        },
        complain_info: {
            content: '反馈类型：医生未明确解决方案 / 问题描述：s收拾收拾收拾收拾收拾收拾 / 期望结果：asd啊SD', // 投诉内容
            create_at: **********, // 投诉时间
        },
        comment_info: {
            comment: '医生一般般吧，不怎么样', // 评价内容
            star: '1', // 评价星级
            create_at: '**********', // 评价时间
            type: '5',
            plat_lable: '',
            pro_lable: '["\\u6ca1\\u6709\\u89e3\\u51b3\\u95ee\\u9898"]',
        },
        prescription_info: {
            prescription_id: '78', // 最后的处方ID
            prescription_code: '747721866301220524',
        },
        doctor_info: {
            cid1_name: '内科', // 医生注册的一级科室
            cid2_name: '消化内科', // 医生注册的二级科室
            is_self_employed: 1, // 是否自雇 1是 0否
            is_concentrate: 1, // 是否精选 1是 0否
            dr_uid: **********,
            expert_id: **********,
            resource_id: **********,
            is_medical_cosmetology: 1, // 是否医美 1是 0否
        },
        patient_info: {
            uid: **********,
            patient_id: **********,
            sex: '男', // 是否精选 1是 0否
            age: 23,
            month: 10, // 是否医美 1是 0否
        },
        patient_mind: 0,
        patient_mind_list: [
            {
                order_id: '1234',
                order_status: '已退款',
                order_amount: 1, // 分
                pay_time: **********,
                refund_start_time: **********,
                refund_end_time: **********,
            },
            {
                order_id: '1234',
                order_status: '已退款',
                order_amount: 1, // 分
                pay_time: **********,
                refund_start_time: **********,
                refund_end_time: **********,
            },
            {
                order_id: '1234',
                order_status: '已退款',
                order_amount: 1, // 分
                pay_time: **********,
                refund_start_time: **********,
                refund_end_time: **********,
            },
            {
                order_id: '1234',
                order_status: '已退款',
                order_amount: 1, // 分
                pay_time: **********,
                refund_start_time: **********,
                refund_end_time: **********,
            },
        ],
        question_info: {
            content: '血红蛋白低怎么回事,早晨起床眼睛肿脸肿',
            pic_urls:
                'https://bj.bcebos.com/muzhi-pic/100026584511?authorization=bce-auth-v1%2F640c67afd1de40d2a7621c76cca1ea63%2F2021-01-18T07%3A19%3A04Z%2F-1%2Fhost%2F7a03e9f9bbfa59c34d29c9a406b0e43709effc2dcd7e585e4e72856c63ad2dbf',
            report_url: 'https://www.baidu.com',
        },
        sku_info: {
            sku_id: 1,
            round_limit: 5,
            duration_limit: 1619582595,
        },
        ly_order_info: {
            lo_id: 'f685d948-4e52-4b0b-85fd-3df9e6aa76b5_34',
            cid1: '内科',
            cid2: '呼吸内科',
            status: 11,
            end_time: 1619582595,
            std_cid1: '内科',
            std_cid2: '呼吸内科',
            risk_tags: 'hit_abuse',
            start_time: 1619582595,
            accept_time: 1619582595,
            create_time: 1619582595,
            close_reason: '12',
            dispatch_type: '1',
            first_reply_time: 1619582595,
            round_used: 2,
            create_type: 1,
            assistant_uid: 0,
            ly_info: {
                dispatch_type_comment: '三甲派单',
            },
        },
    },
};
