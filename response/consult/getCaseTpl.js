module.exports = {
    'errno': 0,
    'errmsg': 'success',
    'data': {
        'base_line': {
            'case_tpl_id': 'base_line',
            'next_case': 'profession_line',
            'full_mark': 0,
            'desc': '底线问题',
            'sub_list': [
                {
                    'id': 2022,
                    'title': '专业错误',
                    'enable': 1, // 是否可选 1-可选 0-不可选
                    'inspect_v': 1, // 是否存在  1-存在 0-不存在
                },
                {
                    'id': 2028,
                    'title': '态度恶劣，辱骂患者或平台',
                    'enable': 1,
                    'inspect_v': 0,
                },
                {
                    'id': 2003,
                    'title': '介入医疗鉴定/纠纷',
                    'enable': 1,
                    'inspect_v': 1,
                },
                {
                    'id': 2004,
                    'title': '指导处方药使用',
                    'enable': 1,
                    'inspect_v': 0,
                },
                {
                    'id': 2029,
                    'title': '协助患者进行违反国家法律的行为（胎儿性别鉴定、买卖器官...)',
                    'enable': 1,
                    'inspect_v': 0,
                },
                {
                    'id': 2025,
                    'title': '留平台之外联系方式/引导用户去其他平台行为',
                    'enable': 1,
                    'inspect_v': 0,
                },
                {
                    'id': 2027,
                    'title': '推荐民营医院',
                    'enable': 1,
                    'inspect_v': 0,
                },
                {
                    'id': 2030,
                    'title': '诊断与咨询内容不相关',
                    'enable': 0,
                    'inspect_v': 0,
                },
                {
                    'id': 2031,
                    'title': '未详细问诊便开具处方/用药建议',
                    'enable': 0,
                    'inspect_v': 0,
                },
                {
                    'id': 2032,
                    'title': '患者明确无开方需求，医生开具处方/用药建议',
                    'enable': 0,
                    'inspect_v': 0,
                },
                {
                    'id': 2033,
                    'title': '给急危重症患者在线开方/用药建议',
                    'enable': 0,
                    'inspect_v': 0,
                },
            ],
        },
        'profession_line': {
            'case_tpl_id': 'profession_line',
            'full_mark': 100,
            'desc': '诊疗规范评价',
            'sub_list': [
                {
                    'id': 3001,
                    'title': '造成严重后果',
                    'enable': 1,
                    'select_v': 0,
                    'deduction_score': 100,
                },
                {
                    'id': 3002,
                    'title': '无问诊',
                    'enable': 1,
                    'select_v': 0,
                    'deduction_score': 40,
                },
                {
                    'id': 3003,
                    'title': '问诊不全',
                    'enable': 1,
                    'select_v': 0,
                    'deduction_score': 20,
                },
                {
                    'id': 3004,
                    'title': '问诊不对',
                    'enable': 1,
                    'select_v': 0,
                    'deduction_score': 10,
                },
                {
                    'id': 3005,
                    'title': '无诊断（无主要诊断）',
                    'enable': 1,
                    'select_v': 0,
                    'deduction_score': 30,
                },
                {
                    'id': 3006,
                    'title': '诊断依据不足（未错诊）',
                    'enable': 1,
                    'select_v': 0,
                    'deduction_score': 10,
                },
                {
                    'id': 3007,
                    'title': '遗漏重症',
                    'enable': 1,
                    'select_v': 0,
                    'deduction_score': 30,
                },
                {
                    'id': 3008,
                    'title': '诊断错误',
                    'enable': 1,
                    'select_v': 0,
                    'deduction_score': 5,
                },
                {
                    'id': 3009,
                    'title': '药物适应症不符或者违反禁忌症',
                    'enable': 1,
                    'select_v': 0,
                    'deduction_score': 30,
                },
                {
                    'id': 3010,
                    'title': '剂量错误',
                    'enable': 1,
                    'select_v': 0,
                    'deduction_score': 30,
                },
                {
                    'id': 3011,
                    'title': '无兜底及生活建议',
                    'enable': 1,
                    'select_v': 0,
                    'deduction_score': 10,
                },
            ],
            'next_case': 'service_line',
        },
        'service_line': {
            'case_tpl_id': 'service_line',
            'full_mark': 100,
            'desc': '服务问题',
            'sub_list': [
                {
                    'id': 3012,
                    'title': '态度敷衍',
                    'enable': 1,
                    'inspect_v': 0,
                    'deduction_score': 10,
                },
                {
                    'id': 3013,
                    'title': '态度恶劣，辱骂患者或平台',
                    'enable': 1,
                    'inspect_v': 0,
                    'deduction_score': 50,
                },
                {
                    'id': 3014,
                    'title': '直接线下就诊',
                    'enable': 1,
                    'inspect_v': 0,
                    'deduction_score': 10,
                },
                {
                    'id': 3015,
                    'title': '平均回复间隔大',
                    'enable': 1,
                    'inspect_v': 0,
                    'deduction_score': 20,
                },
                {
                    'id': 3016,
                    'title': '强推处方',
                    'enable': 1,
                    'inspect_v': 0,
                    'deduction_score': 10,
                },
                {
                    'id': 3017,
                    'title': '推荐民营医院',
                    'enable': 1,
                    'inspect_v': 0,
                    'deduction_score': 50,
                },
                {
                    'id': 3018,
                    'title': '引导私联',
                    'enable': 1,
                    'inspect_v': 0,
                    'deduction_score': 50,
                },
                {
                    'id': 3019,
                    'title': '介入医疗纠纷',
                    'enable': 1,
                    'inspect_v': 0,
                    'deduction_score': 50,
                },
                {
                    'id': 3020,
                    'title': '协助患者进行违反国家法律的行为',
                    'enable': 1,
                    'inspect_v': 0,
                    'deduction_score': 50,
                },
            ],
        },
    },
};