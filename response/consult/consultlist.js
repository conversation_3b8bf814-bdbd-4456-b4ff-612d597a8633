module.exports = {
    "errno": 0,
    "data": {
        "total": 21,
        "list": [{
            "consult_id": 66789730,
            "issue_id": 260798116,
            "category": 2,
            "pack_brief": "3轮/2小时",
            "description": "医生，急诊科的医生，在处理病人流血的伤口时，会不会首先化验传染病之类的！比如艾滋病，什么的",
            "cname1": "感染科",
            "cname2": "传染科综合",
            "bill_service_seconds": 1,
            "close_type": "408",
            "ask_type_desc": "续费",
            "pay_status_desc": "已支付",
            "status_desc": "已关闭",
            "close_at": "-",
            "create_at": 1574668674,
            "refundable": 1,
            "refund_desc": "退款"
        }, {
            "consult_id": 55119154,
            "issue_id": 249146210,
            "category": 2,
            "pack_brief": "30\u5206\u949f",
            "description": "\u6392\u5375\u671f\u540c\u623f\u540e\u51fa\u73b0\u8910\u8272\u5206\u6ccc\u7269\u600e\u4e48\u56de\u4e8b",
            "cname1": "\u5987\u4ea7\u79d1",
            "cname2": "\u5987\u79d1\u7efc\u5408",
            "bill_service_seconds": 0,
            "close_type_desc": "-",
            "ask_type_desc": "\u7528\u6237\u81ea\u4e3b\u9009\u62e9",
            "pay_status_desc": "已支付",
            "status_desc": "已关闭",
            "close_at": "-",
            "create_at": 1574668673,
            "refundable": 0,
            "refund_desc": "\u670d\u52a1\u4e2d"
        }, {
            "consult_id": 55119153,
            "issue_id": 249146209,
            "category": 2,
            "pack_brief": "30\u5206\u949f",
            "description": "\u6000\u5b55\u4e24\u4e2a\u534a\u6708\u5c0f\u8179\u4e2d\u95f4\u624e\u9488\u75bc\u75db",
            "cname1": "\u5987\u4ea7\u79d1",
            "cname2": "\u4ea7\u79d1",
            "bill_service_seconds": 0,
            "close_type_desc": "-",
            "ask_type_desc": "\u7528\u6237\u81ea\u4e3b\u9009\u62e9",
            "pay_status_desc": "无需支付",
            "status_desc": "待认领",
            "close_at": "-",
            "create_at": 1574668673,
            "refundable": 0,
            "refund_desc": "服务中"
        }, {
            "consult_id": 55119152,
            "issue_id": 249146208,
            "category": 2,
            "pack_brief": "30\u5206\u949f",
            "description": "\u5de6\u8155\u5173\u8282\u8bf8\u6784\u6210\u9aa8\u672a\u89c1\u660e\u663e\u9aa8\u8d28\u5f02\u5e38\u5f81\u8c61,\u5de6\u8155\u5173\u8282\u95f4\u9699\u53ef\u3002",
            "cname1": "\u5916\u79d1",
            "cname2": "\u9aa8\u79d1",
            "bill_service_seconds": 0,
            "close_type_desc": "-",
            "ask_type_desc": "\u7528\u6237\u81ea\u4e3b\u9009\u62e9",
            "pay_status_desc": "\u65e0\u9700\u652f\u4ed8",
            "status_desc": "\u5f85\u8ba4\u9886",
            "close_at": "-",
            "create_at": 1574668673,
            "refundable": 0,
            "refund_desc": "\u670d\u52a1\u4e2d"
        }, {
            "consult_id": 55119151,
            "issue_id": 249146207,
            "category": 2,
            "pack_brief": "30\u5206\u949f",
            "description": "\u9488\u7078\u540e\u4e0b\u5df4\u53d1\u9ebb\u600e\u4e48\u56de\u4e8b?",
            "cname1": "\u4e2d\u533b\u79d1",
            "cname2": "\u9488\u7078\u7406\u7597\u79d1",
            "bill_service_seconds": 0,
            "close_type_desc": "-",
            "ask_type_desc": "\u7528\u6237\u81ea\u4e3b\u9009\u62e9",
            "pay_status_desc": "\u65e0\u9700\u652f\u4ed8",
            "status_desc": "\u5f85\u8ba4\u9886",
            "close_at": "-",
            "create_at": 1574668673,
            "refundable": 0,
            "refund_desc": "\u670d\u52a1\u4e2d"
        }, {
            "consult_id": 55119150,
            "issue_id": 249146206,
            "category": 2,
            "pack_brief": "30\u5206\u949f",
            "description": "\u80c3\u7aa6\u53ef\u89c1\u591a\u53d1\u6027\u7ea2\u6591\u662f\u4ec0\u4e48\u610f\u601d",
            "cname1": "\u5185\u79d1",
            "cname2": "\u6d88\u5316\u5185\u79d1",
            "bill_service_seconds": 0,
            "close_type_desc": "-",
            "ask_type_desc": "\u7528\u6237\u81ea\u4e3b\u9009\u62e9",
            "pay_status_desc": "\u65e0\u9700\u652f\u4ed8",
            "status_desc": "\u5f85\u8ba4\u9886",
            "close_at": "-",
            "create_at": 1574668672,
            "refundable": 0,
            "refund_desc": "\u670d\u52a1\u4e2d"
        }, {
            "consult_id": 55119149,
            "issue_id": 249146205,
            "category": 2,
            "pack_brief": "30\u5206\u949f",
            "description": "\u6253\u4e86\u4e5d\u4ef7\u75ab\u82d7\u7b2c\u4e8c\u5929\u80fd\u5403\u5ca9\u9e7f\u4e73\u5eb7\u80f6\u56ca\u5417",
            "cname1": "\u7528\u836f\u6307\u5bfc",
            "cname2": "\u5b89\u5168\u7528\u836f",
            "bill_service_seconds": 0,
            "close_type_desc": "-",
            "ask_type_desc": "\u7528\u6237\u81ea\u4e3b\u9009\u62e9",
            "pay_status_desc": "\u65e0\u9700\u652f\u4ed8",
            "status_desc": "\u5f85\u8ba4\u9886",
            "close_at": "-",
            "create_at": 1574668671,
            "refundable": 0,
            "refund_desc": "\u670d\u52a1\u4e2d"
        }, {
            "consult_id": 55119148,
            "issue_id": 249146204,
            "category": 2,
            "pack_brief": "\u5927\u641c\u4e13\u5bb6-\u4e09\u7532",
            "description": "\u7528\u4e86\u5b50\u5bab\u6392\u6bd2\u7684\u836f\u4e38\u9634\u9053\u91cc\u6709\u5f88\u591a\u9ec4\u8272\u7684\u5206\u6ccc\u7269\u8fd8\u5e26\u76ae\u5b50\u4e00\u6837\u7684\u4e1c\u897f",
            "cname1": "\u5987\u4ea7\u79d1",
            "cname2": "\u5987\u79d1\u7efc\u5408",
            "bill_service_seconds": 0,
            "close_type_desc": "-",
            "ask_type_desc": "\u7528\u6237\u81ea\u4e3b\u9009\u62e9",
            "pay_status_desc": "\u5df2\u652f\u4ed8",
            "status_desc": "\u5df2\u8ba4\u9886",
            "close_at": "-",
            "create_at": 1574668671,
            "refundable": 0,
            "refund_desc": "\u670d\u52a1\u4e2d"
        }, {
            "consult_id": 55119147,
            "issue_id": 249146203,
            "category": 2,
            "pack_brief": "30\u5206\u949f",
            "description": "\u9ad8\u539f\u53cd\u5e94\u52a0\u611f\u5192\u7684\u75c7\u72b6",
            "cname1": "\u5185\u79d1",
            "cname2": "\u547c\u5438\u5185\u79d1",
            "bill_service_seconds": 0,
            "close_type_desc": "-",
            "ask_type_desc": "\u7528\u6237\u81ea\u4e3b\u9009\u62e9",
            "pay_status_desc": "\u65e0\u9700\u652f\u4ed8",
            "status_desc": "\u5f85\u8ba4\u9886",
            "close_at": "-",
            "create_at": 1574668671,
            "refundable": 0,
            "refund_desc": "\u670d\u52a1\u4e2d"
        }, {
            "consult_id": 55119146,
            "issue_id": 249146202,
            "category": 2,
            "pack_brief": "30\u5206\u949f",
            "description": "11\u4e2a\u6708\u5927\u7684\u5b9d\u5b9d\u6700\u8fd1\u5176\u4ed6\u7684\u4e1c\u897f\u90fd\u5403\uff0c\u5c31\u662f\u4e0d\u80af\u5403\u7c73\u996d\u600e\u4e48\u529e\uff1f",
            "cname1": "\u513f\u79d1",
            "cname2": "\u513f\u5185\u79d1",
            "bill_service_seconds": 0,
            "close_type_desc": "-",
            "ask_type_desc": "\u7528\u6237\u81ea\u4e3b\u9009\u62e9",
            "pay_status_desc": "\u65e0\u9700\u652f\u4ed8",
            "status_desc": "\u5df2\u8ba4\u9886",
            "close_at": "-",
            "create_at": 1574668671,
            "refundable": 0,
            "refund_desc": "\u670d\u52a1\u4e2d"
        }]
    }
};
