module.exports = {
    errno: 0,
    errmsg: '',
    status: 0,
    msg: '',
    logId: 3832010154,
    time: 1703680047,
    data: {
        'list': [
            {
                'agreementId': 693,
                'createTime': '2025-01-07 23:08:10',
                'createUser': 'mao',
                'id': '7',
                'title': '百度健康（医生版）隐私政策',
                'url': 'https://muzhi.baidu.com/dcwap/activity/main#/newArticle?id=64bf041e556bb1c85d63ddd4&type=preview',
                'version': 4,
            },
            {
                'agreementId': 693,
                'createTime': '2025-01-07 20:11:33',
                'id': '3',
                'title': '',
                'url': 'https://muzhi.baidu.com/dcwap/activity/main#/newArticle?id=64bf041e556bb1c85d63ddd4&type=preview',
                'version': 3,
            },
            {
                'agreementId': 693,
                'createTime': '2025-01-07 20:11:33',
                'id': '2',
                'title': '',
                'url': 'https://muzhi.baidu.com/dcwap/activity/main#/newArticle?id=64bf041e556bb1c85d63ddd4&type=preview',
                'version': 2,
            },
            {
                'agreementId': 693,
                'createTime': '2025-01-07 15:32:04',
                'createUser': 'mao',
                'id': '1',
                'title': '百度健康（医生版）隐私政策',
                'url': 'https://muzhi.baidu.com/dcwap/activity/main#/newArticle?id=64bf041e556bb1c85d63ddd4&type=preview',
                'version': 1,
            },
            {
                'agreementId': 693,
                'createTime': '2025-01-07 23:08:10',
                'createUser': 'mao',
                'id': '7',
                'title': '百度健康（医生版）隐私政策',
                'url': 'https://muzhi.baidu.com/dcwap/activity/main#/newArticle?id=64bf041e556bb1c85d63ddd4&type=preview',
                'version': 4,
            },
            {
                'agreementId': 693,
                'createTime': '2025-01-07 20:11:33',
                'id': '3',
                'title': '',
                'url': 'https://muzhi.baidu.com/dcwap/activity/main#/newArticle?id=64bf041e556bb1c85d63ddd4&type=preview',
                'version': 3,
            },
            {
                'agreementId': 693,
                'createTime': '2025-01-07 20:11:33',
                'id': '2',
                'title': '',
                'url': 'https://muzhi.baidu.com/dcwap/activity/main#/newArticle?id=64bf041e556bb1c85d63ddd4&type=preview',
                'version': 2,
            },
            {
                'agreementId': 693,
                'createTime': '2025-01-07 15:32:04',
                'createUser': 'mao',
                'id': '1',
                'title': '百度健康（医生版）隐私政策',
                'url': 'https://muzhi.baidu.com/dcwap/activity/main#/newArticle?id=64bf041e556bb1c85d63ddd4&type=preview',
                'version': 1,
            },
            {
                'agreementId': 693,
                'createTime': '2025-01-07 23:08:10',
                'createUser': 'mao',
                'id': '7',
                'title': '百度健康（医生版）隐私政策',
                'url': 'https://muzhi.baidu.com/dcwap/activity/main#/newArticle?id=64bf041e556bb1c85d63ddd4&type=preview',
                'version': 4,
            },
            {
                'agreementId': 693,
                'createTime': '2025-01-07 20:11:33',
                'id': '3',
                'title': '',
                'url': 'https://muzhi.baidu.com/dcwap/activity/main#/newArticle?id=64bf041e556bb1c85d63ddd4&type=preview',
                'version': 3,
            },
            {
                'agreementId': 693,
                'createTime': '2025-01-07 20:11:33',
                'id': '2',
                'title': '',
                'url': 'https://muzhi.baidu.com/dcwap/activity/main#/newArticle?id=64bf041e556bb1c85d63ddd4&type=preview',
                'version': 2,
            },
            {
                'agreementId': 693,
                'createTime': '2025-01-07 15:32:04',
                'createUser': 'mao',
                'id': '1',
                'title': '百度健康（医生版）隐私政策',
                'url': 'https://muzhi.baidu.com/dcwap/activity/main#/newArticle?id=64bf041e556bb1c85d63ddd4&type=preview',
                'version': 1,
            },
            {
                'agreementId': 693,
                'createTime': '2025-01-07 23:08:10',
                'createUser': 'mao',
                'id': '7',
                'title': '百度健康（医生版）隐私政策',
                'url': 'https://muzhi.baidu.com/dcwap/activity/main#/newArticle?id=64bf041e556bb1c85d63ddd4&type=preview',
                'version': 4,
            },
            {
                'agreementId': 693,
                'createTime': '2025-01-07 20:11:33',
                'id': '3',
                'title': '',
                'url': 'https://muzhi.baidu.com/dcwap/activity/main#/newArticle?id=64bf041e556bb1c85d63ddd4&type=preview',
                'version': 3,
            },
            {
                'agreementId': 693,
                'createTime': '2025-01-07 20:11:33',
                'id': '2',
                'title': '',
                'url': 'https://muzhi.baidu.com/dcwap/activity/main#/newArticle?id=64bf041e556bb1c85d63ddd4&type=preview',
                'version': 2,
            },
            {
                'agreementId': 693,
                'createTime': '2025-01-07 15:32:04',
                'createUser': 'mao',
                'id': '1',
                'title': '百度健康（医生版）隐私政策',
                'url': 'https://muzhi.baidu.com/dcwap/activity/main#/newArticle?id=64bf041e556bb1c85d63ddd4&type=preview',
                'version': 1,
            },
            {
                'agreementId': 693,
                'createTime': '2025-01-07 23:08:10',
                'createUser': 'mao',
                'id': '7',
                'title': '百度健康（医生版）隐私政策',
                'url': 'https://muzhi.baidu.com/dcwap/activity/main#/newArticle?id=64bf041e556bb1c85d63ddd4&type=preview',
                'version': 4,
            },
            {
                'agreementId': 693,
                'createTime': '2025-01-07 20:11:33',
                'id': '3',
                'title': '',
                'url': 'https://muzhi.baidu.com/dcwap/activity/main#/newArticle?id=64bf041e556bb1c85d63ddd4&type=preview',
                'version': 3,
            },
            {
                'agreementId': 693,
                'createTime': '2025-01-07 20:11:33',
                'id': '2',
                'title': '',
                'url': 'https://muzhi.baidu.com/dcwap/activity/main#/newArticle?id=64bf041e556bb1c85d63ddd4&type=preview',
                'version': 2,
            },
            {
                'agreementId': 693,
                'createTime': '2025-01-07 15:32:04',
                'createUser': 'mao',
                'id': '1',
                'title': '百度健康（医生版）隐私政策',
                'url': 'https://muzhi.baidu.com/dcwap/activity/main#/newArticle?id=64bf041e556bb1c85d63ddd4&type=preview',
                'version': 1,
            },
        ],
    },
};
