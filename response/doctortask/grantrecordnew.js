module.exports = {
    'errno': 0,
    'errmsg': 'success',
    'data': {
        'list': [
            {
                'task_rule_id': 1111,
                'task_rule_condition': '任务名称-完成备案',
                task_reward_type: 1,
                'reward_name': ['奖品1,京东卡100,现金1000'],
                'uid': 246677827,
                'name': '李航',
                'task_finish_time': 1626058822,
                'task_reward_receive_status': 1,
                'claim_status_desc': '待确认',
                'grant_at': '1626058889',
                'remark': '备注，医生电话打不通',
            },
            {
                'task_rule_id': 122,
                'task_rule_condition': '任务名称-完成备案222',
                task_reward_type: 0,
                'reward_name': ['奖品1,京东卡100,现金1000'],
                'dr_utask_rule_id': 2466778222,
                'dr_name': '李并',
                'finish_at': 1626058822,
                'claim_status': 1,
                'claim_status_desc': '待确认',
                'grant_at': '0',
                'remark': '医生不想要',
            },
        ],
        'total': 10001,
    },
    'logtask_rule_id': 77226869,
    'time': 1626058877,
};
